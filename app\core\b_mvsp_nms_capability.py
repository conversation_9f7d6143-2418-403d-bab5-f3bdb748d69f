"""
承载B网MVSP_NMS配置生成能力模块

处理IP地址列表的增加、删除和修改操作，支持多厂商语法
"""

import re
import ipaddress
from typing import Dict, Any, List, Tuple, AsyncGenerator
from loguru import logger

from app.core.base_capability import LLMBasedCapability
from app.models.request import ConfigGenerationRequest


class BMVSPNMSCapability(LLMBasedCapability):
    """承载B网MVSP_NMS配置生成能力类"""

    def __init__(self):
        super().__init__("b-mvsp-nms-capability")
        
        # 华为设备IP前缀列表模板
        self.huawei_templates = {
            "add": "ip ip-prefix pl-MVSP_NMS_B-v4-in permit {ip} {mask}",
            "add_with_index": "ip ip-prefix pl-MVSP_NMS_B-v4-in index {index} permit {ip} {mask}",
            "delete": "undo ip ip-prefix pl-MVSP_NMS_B-v4-in index {index}",
            "comment": "#"
        }
        
        # 思科设备IP前缀列表模板
        self.cisco_templates = {
            "add": "ip prefix-list MVSP_NMS_B_v4_in seq {seq} permit {ip}/{mask}",
            "delete": "no ip prefix-list MVSP_NMS_B_v4_in seq {seq}",
            "comment": "!"
        }

    def _extract_ip_operations(self, user_input: str) -> List[Dict[str, Any]]:
        """从用户输入中提取IP地址操作信息"""
        operations = []
        
        # 匹配IP地址范围的正则表达式
        ip_pattern = r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})/(\d{1,2})'
        ip_pattern_with_space = r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s+(\d{1,2})'
        
        # 查找所有IP地址
        ip_matches = re.findall(ip_pattern, user_input)
        ip_matches_space = re.findall(ip_pattern_with_space, user_input)
        
        # 合并两种格式的匹配结果
        all_ip_matches = ip_matches + ip_matches_space
        
        # 分析操作类型
        text_lower = user_input.lower()
        
        for ip, mask in all_ip_matches:
            try:
                # 验证IP地址格式
                ipaddress.IPv4Network(f"{ip}/{mask}", strict=False)
                
                operation_type = "add"  # 默认为新增
                
                # 判断操作类型
                if "调整" in user_input or "原接收地址" in user_input:
                    if f"{ip}/{mask}" in user_input:
                        # 查找这个IP前后的文本来判断是原地址还是新地址
                        ip_index = user_input.find(f"{ip}/{mask}")
                        before_text = user_input[:ip_index].lower()
                        
                        if "原" in before_text[-20:] or "由" in before_text[-20:]:
                            operation_type = "modify_old"
                        elif "调整为" in before_text[-20:] or "为" in before_text[-20:]:
                            operation_type = "modify_new"
                        else:
                            operation_type = "add"
                elif "新增" in user_input:
                    operation_type = "add"
                elif "删除" in user_input or "回收" in user_input:
                    operation_type = "delete"
                
                operations.append({
                    "type": operation_type,
                    "ip": ip,
                    "mask": int(mask),
                    "cidr": f"{ip}/{mask}"
                })
                
            except ValueError as e:
                logger.warning(f"无效的IP地址格式: {ip}/{mask}, 错误: {e}")
                continue
        
        return operations

    def _extract_existing_config_info(self, user_input: str) -> List[Dict[str, Any]]:
        """从用户输入中提取现有配置信息"""
        existing_configs = []
        
        # 匹配现有配置的正则表达式
        config_pattern = r'ip\s+ip-prefix\s+pl-MVSP_NMS_B-v4-in\s+index\s+(\d+)\s+permit\s+(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s+(\d{1,2})'
        
        matches = re.findall(config_pattern, user_input)
        
        for index, ip, mask in matches:
            existing_configs.append({
                "index": int(index),
                "ip": ip,
                "mask": int(mask),
                "cidr": f"{ip}/{mask}"
            })
        
        return existing_configs

    def _generate_huawei_config(self, operations: List[Dict[str, Any]], existing_configs: List[Dict[str, Any]]) -> str:
        """生成华为设备配置"""
        config_lines = []
        
        # 添加注释说明
        config_lines.append(f"{self.huawei_templates['comment']}原有的配置")
        
        # 显示原有配置
        for config in existing_configs:
            line = self.huawei_templates['add_with_index'].format(
                index=config['index'],
                ip=config['ip'],
                mask=config['mask']
            )
            config_lines.append(line + " ")
        
        config_lines.append("")
        config_lines.append(f"{self.huawei_templates['comment']}新增的配置")
        
        # 处理新增和修改操作
        for op in operations:
            if op['type'] in ['add', 'modify_new']:
                line = self.huawei_templates['add'].format(
                    ip=op['ip'],
                    mask=op['mask']
                )
                config_lines.append(line)
        
        # 处理删除操作
        delete_operations = [op for op in operations if op['type'] == 'delete']
        modify_old_operations = [op for op in operations if op['type'] == 'modify_old']
        
        if delete_operations or modify_old_operations:
            config_lines.append(f"{self.huawei_templates['comment']}回收原有地址列表")
            
            # 删除原有的配置项
            for config in existing_configs:
                # 检查是否需要删除这个配置
                should_delete = False
                
                for op in modify_old_operations:
                    if config['ip'] == op['ip'] and config['mask'] == op['mask']:
                        should_delete = True
                        break
                
                if should_delete:
                    line = self.huawei_templates['delete'].format(index=config['index'])
                    config_lines.append(line)
        
        config_lines.append(f"{self.huawei_templates['comment']}")
        
        return '\n'.join(config_lines)

    def _generate_cisco_config(self, operations: List[Dict[str, Any]], existing_configs: List[Dict[str, Any]]) -> str:
        """生成思科设备配置"""
        config_lines = []
        
        # 添加注释说明
        config_lines.append(f"{self.cisco_templates['comment']} Original configuration")
        
        # 处理新增操作
        seq_num = 10
        for op in operations:
            if op['type'] in ['add', 'modify_new']:
                line = self.cisco_templates['add'].format(
                    seq=seq_num,
                    ip=op['ip'],
                    mask=op['mask']
                )
                config_lines.append(line)
                seq_num += 10
        
        return '\n'.join(config_lines)

    async def generate_config(self, request: ConfigGenerationRequest) -> str:
        """生成配置（非流式返回）"""
        try:
            # 提取用户输入
            user_input = self._extract_user_input_from_messages(request.messages)
            
            # 提取IP操作信息
            operations = self._extract_ip_operations(user_input)
            existing_configs = self._extract_existing_config_info(user_input)
            
            logger.info(f"提取到 {len(operations)} 个IP操作, {len(existing_configs)} 个现有配置")
            
            # 根据厂商生成配置
            vendor = getattr(request, 'vendor', 'huawei').lower()
            
            if vendor == 'cisco':
                config_content = self._generate_cisco_config(operations, existing_configs)
            else:  # 默认华为
                config_content = self._generate_huawei_config(operations, existing_configs)
            
            return config_content
            
        except Exception as e:
            logger.error(f"B_MVSP_NMS配置生成失败: {str(e)}")
            return f"配置生成失败: {str(e)}"

    async def generate_config_stream(self, request: ConfigGenerationRequest) -> AsyncGenerator[Dict[str, Any], None]:
        """生成配置（流式返回）"""
        try:
            # 生成配置内容
            config_content = await self.generate_config(request)
            
            # 将内容转换为流式响应
            async for chunk in self._create_content_chunks(config_content):
                yield chunk
                
        except Exception as e:
            logger.error(f"B_MVSP_NMS流式配置生成失败: {str(e)}")
            yield self._create_error_chunk(f"配置生成失败: {str(e)}")

    def _extract_user_input_from_messages(self, messages: list) -> str:
        """从消息列表中提取用户输入内容"""
        # 从后往前查找最新的用户消息
        for message in reversed(messages):
            if message.role == "user":
                # 优先从attachment字段获取附件内容
                if hasattr(message, 'attachment') and message.attachment is not None:
                    if isinstance(message.attachment, str):
                        return message.attachment.strip()
                
                # 从content获取
                return message.content.strip()
        return ""


# 全局B_MVSP_NMS能力实例
b_mvsp_nms_capability = BMVSPNMSCapability()
