#!/usr/bin/env python3
"""
简化版承载B网MVSP_NMS配置生成能力演示脚本
"""

import asyncio
from unittest.mock import patch
from app.core.b_mvsp_nms_capability import BMVSPNMSCapability
from app.services.intent_recognition import IntentRecognitionService
from app.models.request import ConfigGenerationRequest, Message


async def demo_intent_recognition():
    """演示意图识别功能"""
    print("=" * 60)
    print("承载B网MVSP_NMS意图识别演示")
    print("=" * 60)
    
    service = IntentRecognitionService()
    
    test_inputs = [
        "因集团IPTV业务需求，需新联通云中原O域资源池通过承载B MVSP_NMS VPN与各省对接",
        "申请资源池承载B网MVSP_NMS接收地址需求",
        "原接收地址范围由************/28调整为***********/27",
        "新增接收地址范围***********/24",
        "普通BGP配置问题"  # 这个应该不被识别为B_MVSP_NMS
    ]
    
    for user_input in test_inputs:
        print(f"\n输入: {user_input}")
        result = service._fallback_analysis(user_input)
        print(f"识别结果: {result['intent_type']}")
        print(f"置信度: {result['confidence']}")


async def demo_llm_prompt():
    """演示LLM提示词构建"""
    print("\n" + "=" * 60)
    print("LLM提示词构建演示")
    print("=" * 60)
    
    capability = BMVSPNMSCapability()
    
    user_input = """因集团IPTV业务需求，需新联通云中原O域资源池通过承载B MVSP_NMS VPN与各省对接，申请资源池承载B网MVSP_NMS接收地址需求如下： 
    原接收地址范围由************/28调整为***********/27（河南）；
    新增接收地址范围***********/24（黑龙江）；"""
    
    print(f"用户输入:\n{user_input}")
    
    # 构建华为设备提示词
    huawei_prompt = capability.prompt_template.format(
        user_input=user_input,
        device_type="router",
        vendor="huawei"
    )
    
    print(f"\n华为设备提示词 (长度: {len(huawei_prompt)} 字符):")
    print("-" * 40)
    print(huawei_prompt[:500] + "..." if len(huawei_prompt) > 500 else huawei_prompt)
    
    # 构建思科设备提示词
    cisco_prompt = capability.prompt_template.format(
        user_input=user_input,
        device_type="router", 
        vendor="cisco"
    )
    
    print(f"\n思科设备提示词 (长度: {len(cisco_prompt)} 字符):")
    print("-" * 40)
    print("提示词内容相同，只是厂商参数不同")


async def demo_mock_config_generation():
    """演示Mock配置生成"""
    print("\n" + "=" * 60)
    print("Mock配置生成演示")
    print("=" * 60)
    
    capability = BMVSPNMSCapability()
    
    # 华为设备Mock响应
    huawei_mock_response = {
        "choices": [{
            "message": {
                "content": """#原有的配置
ip ip-prefix pl-MVSP_NMS_B-v4-in index 10 permit ************ 28 

#新增的配置
ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 27
ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 24
#回收原有地址列表
undo ip ip-prefix pl-MVSP_NMS_B-v4-in index 10
#"""
            }
        }]
    }
    
    # 思科设备Mock响应
    cisco_mock_response = {
        "choices": [{
            "message": {
                "content": """! Original configuration
ip prefix-list MVSP_NMS_B_v4_in seq 10 permit ***********/27
ip prefix-list MVSP_NMS_B_v4_in seq 20 permit ***********/24"""
            }
        }]
    }
    
    messages = [
        Message(
            role="user",
            content="原接收地址范围由************/28调整为***********/27；新增接收地址范围***********/24"
        )
    ]
    
    # 华为设备配置生成
    print("华为设备配置生成:")
    print("-" * 30)
    
    huawei_request = ConfigGenerationRequest(
        messages=messages,
        device_type="router",
        vendor="huawei"
    )
    
    with patch.object(capability, '_call_llm', return_value=huawei_mock_response):
        huawei_config = await capability.generate_config(huawei_request)
        print(huawei_config)
    
    # 思科设备配置生成
    print("\n思科设备配置生成:")
    print("-" * 30)
    
    cisco_request = ConfigGenerationRequest(
        messages=messages,
        device_type="router",
        vendor="cisco"
    )
    
    with patch.object(capability, '_call_llm', return_value=cisco_mock_response):
        cisco_config = await capability.generate_config(cisco_request)
        print(cisco_config)


async def demo_stream_generation():
    """演示流式配置生成"""
    print("\n" + "=" * 60)
    print("流式配置生成演示")
    print("=" * 60)
    
    capability = BMVSPNMSCapability()
    
    # Mock流式响应
    async def mock_stream():
        chunks = [
            {
                "id": "chatcmpl-1",
                "object": "chat.completion.chunk",
                "choices": [{
                    "index": 0,
                    "delta": {"role": "assistant", "content": "#新增的配置\n"},
                    "finish_reason": None
                }]
            },
            {
                "id": "chatcmpl-2",
                "object": "chat.completion.chunk", 
                "choices": [{
                    "index": 0,
                    "delta": {"content": "ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 24\n"},
                    "finish_reason": None
                }]
            },
            {
                "id": "chatcmpl-3",
                "object": "chat.completion.chunk",
                "choices": [{
                    "index": 0,
                    "delta": {"content": "#\n"},
                    "finish_reason": "stop"
                }]
            }
        ]
        
        for chunk in chunks:
            yield chunk
    
    messages = [Message(role="user", content="新增承载B网MVSP_NMS接收地址范围***********/24")]
    request = ConfigGenerationRequest(messages=messages, device_type="router", vendor="huawei")
    
    with patch.object(capability, '_call_llm_stream', return_value=mock_stream()):
        print("流式响应:")
        chunk_count = 0
        async for chunk in capability.generate_config_stream(request):
            chunk_count += 1
            content = chunk['choices'][0]['delta'].get('content', '')
            finish_reason = chunk['choices'][0].get('finish_reason')
            print(f"Chunk {chunk_count}: {repr(content)} (finish: {finish_reason})")


async def main():
    """主演示函数"""
    print("承载B网MVSP_NMS简化版配置生成能力演示")
    print("=" * 60)
    print("注意: 此版本使用LLM进行智能分析，不做复杂的IP解析")
    print("=" * 60)
    
    try:
        await demo_intent_recognition()
        await demo_llm_prompt()
        await demo_mock_config_generation()
        await demo_stream_generation()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("简化版实现的优势:")
        print("1. 代码更简洁，易于维护")
        print("2. 充分利用LLM的智能分析能力")
        print("3. 减少了复杂的正则表达式和IP解析逻辑")
        print("4. LLM能够更好地理解自然语言需求")
        print("5. 支持更灵活的配置生成场景")
        print("=" * 60)
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
