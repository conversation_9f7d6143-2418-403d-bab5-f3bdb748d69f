# 承载B网MVSP_NMS配置生成能力 PRD

## 1. 产品概述

### 1.1 产品背景
随着集团IPTV业务的快速发展，需要通过承载B网MVSP_NMS VPN与各省进行对接。在此过程中，经常需要对IP地址列表进行管理，包括地址范围的调整、新增和删除操作。传统的手工配置方式效率低下且容易出错，因此需要一个智能化的配置生成工具。

### 1.2 产品定位
承载B网MVSP_NMS配置生成能力是数通设备配置生成智能体的专业化功能模块，专门处理IP地址列表管理相关的配置生成需求。

### 1.3 目标用户
- 网络工程师
- 运维人员
- 网络架构师
- 技术支持人员

## 2. 需求分析

### 2.1 业务需求
1. **IP地址范围调整**: 支持将现有IP地址范围调整为新的范围
2. **新增IP地址范围**: 支持添加新的IP地址范围到现有配置中
3. **删除IP地址范围**: 支持删除不再需要的IP地址范围
4. **多厂商支持**: 支持华为、思科等主流厂商的配置语法
5. **自动化配置生成**: 根据用户输入自动生成完整的配置命令

### 2.2 功能需求
1. **意图识别**: 能够准确识别用户关于承载B网MVSP_NMS的配置需求
2. **IP地址解析**: 能够从自然语言中提取IP地址和操作类型
3. **配置生成**: 能够生成符合厂商语法的配置命令
4. **错误处理**: 能够处理无效IP地址和异常情况
5. **流式响应**: 支持实时流式配置生成

### 2.3 非功能需求
1. **性能**: 配置生成响应时间 < 3秒
2. **准确性**: IP地址解析准确率 > 95%
3. **可扩展性**: 支持新增厂商配置模板
4. **可维护性**: 代码结构清晰，易于维护和扩展

## 3. 产品功能

### 3.1 核心功能

#### 3.1.1 意图识别
- **功能描述**: 识别用户输入中的承载B网MVSP_NMS相关意图
- **触发条件**: 用户输入包含关键词："承载B网"、"MVSP_NMS"、"接收地址"、"发送地址"、"地址范围"等
- **识别准确率**: > 90%

#### 3.1.2 IP地址解析
- **功能描述**: 从用户输入中提取IP地址和操作类型
- **支持格式**: 
  - CIDR格式：************/28
  - 空格分隔格式：************ 28
- **支持操作**: 
  - 调整（modify）
  - 新增（add）
  - 删除（delete）

#### 3.1.3 配置生成
- **功能描述**: 根据解析结果生成设备配置命令
- **支持厂商**: 华为、思科
- **输出格式**: 
  - 华为：ip ip-prefix pl-MVSP_NMS_B-v4-in permit/undo
  - 思科：ip prefix-list MVSP_NMS_B_v4_in seq permit

### 3.2 辅助功能

#### 3.2.1 现有配置识别
- **功能描述**: 识别用户输入中的现有配置信息
- **支持格式**: ip ip-prefix配置命令格式

#### 3.2.2 配置验证
- **功能描述**: 验证IP地址格式的有效性
- **验证规则**: 符合IPv4地址和子网掩码规范

## 4. 技术实现

### 4.1 系统架构

```
用户输入 → 意图识别 → IP地址解析 → 配置生成 → 输出结果
    ↓         ↓           ↓           ↓         ↓
  自然语言   LLM分析    正则匹配    模板渲染   配置命令
```

### 4.2 核心组件

#### 4.2.1 BMVSPNMSCapability类
- **继承关系**: 继承自LLMBasedCapability
- **主要方法**:
  - `_extract_ip_operations()`: 提取IP操作信息
  - `_extract_existing_config_info()`: 提取现有配置信息
  - `_generate_huawei_config()`: 生成华为配置
  - `_generate_cisco_config()`: 生成思科配置

#### 4.2.2 意图识别扩展
- **新增意图类型**: B_MVSP_NMS
- **关键词匹配**: 基于预定义关键词列表进行匹配
- **LLM增强**: 使用大语言模型提高识别准确率

#### 4.2.3 能力路由器集成
- **注册机制**: 在CapabilityRouter中注册新能力
- **路由逻辑**: 根据意图类型自动路由到对应处理器

### 4.3 数据流程

#### 4.3.1 输入处理
1. 接收用户输入消息
2. 提取文本内容（支持附件）
3. 进行意图识别
4. 路由到B_MVSP_NMS能力处理器

#### 4.3.2 解析处理
1. 使用正则表达式提取IP地址
2. 分析操作类型（新增/修改/删除）
3. 验证IP地址格式有效性
4. 提取现有配置信息

#### 4.3.3 配置生成
1. 根据厂商类型选择配置模板
2. 生成新增配置命令
3. 生成删除配置命令
4. 组装完整配置输出

## 5. 使用场景

### 5.1 典型场景1：地址范围调整
**用户输入**:
```
因集团IPTV业务需求，需新联通云中原O域资源池通过承载B MVSP_NMS VPN与各省对接，
申请资源池承载B网MVSP_NMS接收地址需求如下： 
原接收地址范围由************/28调整为***********/27（河南）；
```

**系统输出**:
```
#原有的配置
ip ip-prefix pl-MVSP_NMS_B-v4-in index 10 permit ************ 28 

#新增的配置
ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 27
#回收原有地址列表
undo ip ip-prefix pl-MVSP_NMS_B-v4-in index 10
#
```

### 5.2 典型场景2：新增地址范围
**用户输入**:
```
新增接收地址范围***********/24（黑龙江）；
```

**系统输出**:
```
#新增的配置
ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 24
#
```

### 5.3 典型场景3：思科设备配置
**用户输入**:
```
新增接收地址范围***********/27和***********/24
```

**系统输出**（厂商=cisco）:
```
! Original configuration
ip prefix-list MVSP_NMS_B_v4_in seq 10 permit ***********/27
ip prefix-list MVSP_NMS_B_v4_in seq 20 permit ***********/24
```

## 6. 测试策略

### 6.1 单元测试
- **IP地址解析测试**: 验证各种格式IP地址的正确解析
- **配置生成测试**: 验证不同厂商配置的正确生成
- **错误处理测试**: 验证异常情况的正确处理

### 6.2 集成测试
- **完整流程测试**: 从用户输入到配置输出的端到端测试
- **多厂商测试**: 验证华为、思科等厂商配置的正确性
- **流式响应测试**: 验证流式配置生成的正确性

### 6.3 性能测试
- **响应时间测试**: 验证配置生成的响应时间
- **并发测试**: 验证系统的并发处理能力
- **压力测试**: 验证系统在高负载下的稳定性

## 7. 部署和运维

### 7.1 部署要求
- **Python版本**: >= 3.8
- **依赖包**: 已在pyproject.toml中定义
- **环境变量**: 需要配置LLM相关环境变量

### 7.2 监控指标
- **请求成功率**: 配置生成成功的请求比例
- **响应时间**: 平均配置生成时间
- **错误率**: IP地址解析错误的比例
- **意图识别准确率**: B_MVSP_NMS意图识别的准确率

### 7.3 日志记录
- **请求日志**: 记录用户输入和系统响应
- **错误日志**: 记录异常情况和错误信息
- **性能日志**: 记录响应时间和性能指标

## 8. 风险和限制

### 8.1 技术风险
- **IP地址解析准确性**: 复杂的自然语言可能导致解析错误
- **厂商语法差异**: 不同厂商的配置语法可能存在差异
- **LLM依赖**: 意图识别依赖外部LLM服务的稳定性

### 8.2 业务风险
- **配置错误**: 生成的配置可能存在错误，需要人工验证
- **安全风险**: IP地址信息可能涉及网络安全敏感信息

### 8.3 限制条件
- **仅支持IPv4**: 当前版本仅支持IPv4地址，不支持IPv6
- **特定场景**: 专门针对承载B网MVSP_NMS场景，不适用于其他场景
- **厂商限制**: 当前仅支持华为和思科，其他厂商需要扩展

## 9. 后续规划

### 9.1 功能扩展
- **IPv6支持**: 增加对IPv6地址的支持
- **更多厂商**: 支持华三、Juniper等更多厂商
- **批量操作**: 支持批量IP地址操作
- **配置验证**: 增加配置语法验证功能

### 9.2 性能优化
- **缓存机制**: 增加配置模板缓存
- **并行处理**: 支持并行IP地址解析
- **响应优化**: 优化配置生成算法

### 9.3 用户体验
- **可视化界面**: 提供Web界面进行配置管理
- **配置预览**: 提供配置预览和确认功能
- **历史记录**: 保存配置生成历史记录

## 10. 总结

承载B网MVSP_NMS配置生成能力是一个专业化的网络配置生成工具，通过智能化的意图识别和IP地址解析，能够自动生成符合厂商规范的配置命令。该功能的实现将显著提高网络工程师的工作效率，减少人工错误，为集团IPTV业务的快速发展提供有力支撑。
