"""
意图识别服务
"""

import json
import re
from typing import Dict, Any
from loguru import logger


from app.config.constants import (
    IntentType, CUSTOMIZED_TABLE_HEADERS, B_MVSP_NMS_KEYWORDS, INVALID_QUESTION_RESPONSE
)
from app.models.request import IntentRecognitionRequest, ConfigGenerationRequest
from app.models.response import IntentRecognitionResponse
from app.utils.llm_client import llm_client


class IntentRecognitionService:
    """意图识别服务类"""

    def __init__(self):
        # LLM意图识别提示词模板
        self.intent_prompt_template = """你是一个数通设备配置意图识别专家。请分析用户输入，识别其意图类型并提取相关信息。

意图类型定义：
1. customized（定制化能力）：用户输入包含类似表格格式的数据，特别是包含以下表头关键词的："CE端BGP AS号","CE端设备","设备型号","CE端口","CE端互联IP地址","VLAN","BFD时延","vpn-instance","rt/rd","接收地址范围","发送地址范围","AS号","终端设备","终端端口","ER互联IP地址","互联带宽","VRF name","vpn申请人","联系方式","备注"

2. open（开放式能力）：用户输入包含示例数据和需求数据，通常有"示例"、"参考"、"模板"等词汇，
   用户希望基于示例学习生成配置

3. general（通用能力）：用户提出的是纯文字问题，询问如何配置某个协议或功能

4. identity（身份识别能力）：用户询问助手的身份、是谁、能做什么等问题

5. b_mvsp_nms（承载B网MVSP_NMS能力）：用户输入涉及承载B网MVSP_NMS的IP地址列表管理，包含以下关键词："承载B网","MVSP_NMS","接收地址","发送地址","地址范围","ip-prefix","pl-MVSP_NMS","调整","新增","删除","原接收地址","新增接收地址","回收","undo"等，通常涉及IP地址范围的增加、删除、修改操作

6. invalid（无效意图）：与网络设备配置无关的问题

用户输入：{user_input}

请以JSON格式返回分析结果，包含以下字段：
{{
    "intent_type": "customized/open/general/identity/b_mvsp_nms/invalid",
    "confidence": 0.0-1.0,
    "device_type": "router/switch/firewall/unknown",
    "vendor": "huawei/cisco/h3c/juniper/unknown",
    "reasoning": "判断理由",
    "extracted_info": {{
        "has_table": true/false,
        "has_examples": true/false,
        "config_keywords": ["关键词列表"]
    }}
}}

只返回JSON，不要其他内容。"""
    
    async def _analyze_intent_with_llm(self, user_input: str) -> Dict[str, Any]:
        """使用LLM分析用户意图"""
        try:
            prompt = self.intent_prompt_template.format(user_input=user_input)

            response = await llm_client.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                stream=False,
                temperature=0.1  # 使用较低温度确保结果稳定
            )

            if "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"].strip()

                # 尝试解析JSON响应
                try:
                    # 清理可能的markdown格式
                    if content.startswith("```json"):
                        content = content[7:]
                    if content.endswith("```"):
                        content = content[:-3]
                    if content.startswith("<think>"):
                        pattern = r'<think>.*?</think>'
                        content=re.sub(pattern, '', content, flags=re.DOTALL)
                    
                    result = json.loads(content)
                    logger.info(f"LLM意图识别结果: {result}")
                    return result

                except json.JSONDecodeError as e:
                    logger.error(f"LLM响应JSON解析失败: {content}, 错误: {e}")
                    return self._fallback_analysis(user_input)
            else:
                logger.error("LLM响应格式不正确")
                return self._fallback_analysis(user_input)

        except Exception as e:
            logger.error(f"LLM意图识别失败: {str(e)}")
            return self._fallback_analysis(user_input)
    
    def _fallback_analysis(self, user_input: str) -> Dict[str, Any]:
        """当LLM分析失败时的备用分析方法"""
        # 简单的关键词匹配作为备用方案
        text_lower = user_input.lower()

        # 检查是否与配置相关
        config_keywords = [
            "配置", "config", "设置", "setup", "命令", "command",
            "接口", "interface", "路由", "route", "vlan", "bgp",
            "ospf", "isis", "协议", "protocol", "承载", "mvsp_nms",
            "接收地址", "发送地址", "地址范围", "ip-prefix", "回收", "地址列表"
        ]

        is_config_related = any(keyword in text_lower for keyword in config_keywords)

        if not is_config_related:
            return {
                "intent_type": IntentType.INVALID.value,
                "confidence": 0.8,
                "device_type": "unknown",
                "vendor": "unknown",
                "reasoning": "内容与网络设备配置无关",
                "extracted_info": {
                    "has_table": False,
                    "has_examples": False,
                    "config_keywords": []
                }
            }

        # 检查是否包含表格
        has_table = "|" in user_input or "表格" in text_lower

        # 检查B_MVSP_NMS关键词
        b_mvsp_nms_keywords_count = sum(1 for keyword in B_MVSP_NMS_KEYWORDS if keyword.lower() in text_lower)

        # 检查定制化表头
        customized_headers_count = sum(1 for header in CUSTOMIZED_TABLE_HEADERS if header.lower() in text_lower)

        if b_mvsp_nms_keywords_count >= 1:
            intent_type = IntentType.B_MVSP_NMS.value
            confidence = 0.8
        elif customized_headers_count >= 3:
            intent_type = IntentType.CUSTOMIZED.value
            confidence = 0.7
        elif has_table and any(word in text_lower for word in ["示例", "参考", "模板"]):
            intent_type = IntentType.OPEN.value
            confidence = 0.6
        else:
            intent_type = IntentType.GENERAL.value
            confidence = 0.5

        return {
            "intent_type": intent_type,
            "confidence": confidence,
            "device_type": "unknown",
            "vendor": "unknown",
            "reasoning": "备用关键词匹配分析",
            "extracted_info": {
                "has_table": has_table,
                "has_examples": "示例" in text_lower or "参考" in text_lower,
                "config_keywords": [kw for kw in config_keywords if kw in text_lower]
            }
        }
    

    
    def _extract_user_input_from_messages(self, messages: list) -> str:
        """从消息列表中提取最新的用户输入"""
        # 从后往前查找最新的用户消息
        for message in reversed(messages):
            if message.role == "user":
                # 如果有attachment字段，将其内容也包含在分析中
                content = message.content.strip()

                if hasattr(message, 'attachment') and message.attachment is not None:
                    # 如果有附件，在内容中添加附件信息用于意图识别
                    if isinstance(message.attachment, str):
                        # TXT文件内容
                        content += f"\n[附件内容: TXT文件]\n{message.attachment[:500]}..."
                    elif isinstance(message.attachment, list) and message.attachment:
                        # Excel数据
                        content += f"\n[附件内容: Excel文件，包含{len(message.attachment)}行数据]"
                        # 添加表头信息用于意图识别
                        if isinstance(message.attachment[0], dict):
                            headers = list(message.attachment[0].keys())
                            content += f"\n表头: {', '.join(headers)}"

                return content

        # 如果没有找到用户消息，返回空字符串
        return ""

    async def recognize_intent_from_messages(self, request: ConfigGenerationRequest) -> IntentRecognitionResponse:
        """从消息格式的请求中识别用户意图"""
        try:
            # 从消息列表中提取最新的用户输入
            user_input = self._extract_user_input_from_messages(request.messages)

            if not user_input:
                return IntentRecognitionResponse(
                    success=False,
                    message="未找到有效的用户输入",
                    code=400,
                    intent_type=IntentType.INVALID.value,
                    confidence=0.0,
                    reasoning="未找到有效的用户输入"
                )

            # 使用LLM进行意图分析
            analysis_result = await self._analyze_intent_with_llm(user_input)

            # 检查是否为无效意图
            if analysis_result["intent_type"] == IntentType.INVALID.value:
                return IntentRecognitionResponse(
                    success=False,
                    message=INVALID_QUESTION_RESPONSE,
                    code=400,
                    intent_type=analysis_result["intent_type"],
                    confidence=analysis_result["confidence"],
                    device_type=analysis_result.get("device_type"),
                    vendor=analysis_result.get("vendor"),
                    reasoning=analysis_result.get("reasoning", "无效意图"),
                    extracted_info=analysis_result.get("extracted_info", {})
                )

            logger.info(f"LLM意图识别: {analysis_result['intent_type']}, 置信度: {analysis_result['confidence']}")

            return IntentRecognitionResponse(
                success=True,
                message="意图识别成功",
                code=200,
                intent_type=analysis_result["intent_type"],
                confidence=analysis_result["confidence"],
                device_type=analysis_result.get("device_type"),
                vendor=analysis_result.get("vendor"),
                reasoning=analysis_result.get("reasoning", "意图识别成功"),
                extracted_info=analysis_result.get("extracted_info", {}),
                user_input=user_input  # 添加提取的用户输入
            )

        except Exception as e:
            logger.error(f"意图识别失败: {str(e)}")
            return IntentRecognitionResponse(
                success=False,
                message=f"意图识别失败: {str(e)}",
                code=500,
                intent_type=IntentType.INVALID.value,
                confidence=0.0,
                reasoning=f"系统错误: {str(e)}"
            )

    async def recognize_intent(self, request: IntentRecognitionRequest) -> IntentRecognitionResponse:
        """识别用户意图（兼容旧接口）"""
        try:
            user_input = request.user_input.strip()

            # 使用LLM进行意图分析
            analysis_result = await self._analyze_intent_with_llm(user_input)

            # 检查是否为无效意图
            if analysis_result["intent_type"] == IntentType.INVALID.value:
                return IntentRecognitionResponse(
                    success=False,
                    message=INVALID_QUESTION_RESPONSE,
                    code=400,
                    intent_type=analysis_result["intent_type"],
                    confidence=analysis_result["confidence"],
                    device_type=analysis_result.get("device_type"),
                    vendor=analysis_result.get("vendor"),
                    reasoning=analysis_result.get("reasoning", "无效意图"),
                    extracted_info=analysis_result.get("extracted_info", {})
                )

            logger.info(f"LLM意图识别: {analysis_result['intent_type']}, 置信度: {analysis_result['confidence']}")

            return IntentRecognitionResponse(
                success=True,
                message="意图识别成功",
                code=200,
                intent_type=analysis_result["intent_type"],
                confidence=analysis_result["confidence"],
                device_type=analysis_result.get("device_type"),
                vendor=analysis_result.get("vendor"),
                reasoning=analysis_result.get("reasoning", "意图识别成功"),
                extracted_info=analysis_result.get("extracted_info", {})
            )

        except Exception as e:
            logger.error(f"意图识别失败: {str(e)}")
            return IntentRecognitionResponse(
                success=False,
                message=f"意图识别失败: {str(e)}",
                code=500,
                intent_type=IntentType.INVALID.value,
                confidence=0.0,
                reasoning=f"系统错误: {str(e)}"
            )


# 全局意图识别服务实例
intent_service = IntentRecognitionService()
