"""
B_MVSP_NMS API集成测试
"""

import pytest
import asyncio
import json
from unittest.mock import patch

from app.services.config_generation import ConfigGenerationService
from app.models.request import ConfigGenerationRequest, Message
from app.config.constants import IntentType


class TestBMVSPNMSAPI:
    """B_MVSP_NMS API集成测试类"""

    def setup_method(self):
        """测试前准备"""
        self.service = ConfigGenerationService()

    @pytest.mark.asyncio
    async def test_full_pipeline_b_mvsp_nms(self):
        """测试完整的B_MVSP_NMS配置生成流程"""
        # 创建测试请求
        messages = [
            Message(
                role="user",
                content="""因集团IPTV业务需求，需新联通云中原O域资源池通过承载B MVSP_NMS VPN与各省对接，申请资源池承载B网MVSP_NMS接收地址需求如下： 
                原接收地址范围由************/28调整为***********/27（河南）；
                新增接收地址范围***********/24（黑龙江）；
                
                #原有的配置
                ip ip-prefix pl-MVSP_NMS_B-v4-in index 10 permit ************ 28"""
            )
        ]
        
        request = ConfigGenerationRequest(
            messages=messages,
            device_type="router",
            vendor="huawei",
            stream=False
        )
        
        # Mock意图识别结果
        mock_intent_response = type('MockResponse', (), {
            'success': True,
            'intent_type': IntentType.B_MVSP_NMS.value,
            'confidence': 0.95,
            'device_type': 'router',
            'vendor': 'huawei',
            'reasoning': '用户询问承载B网MVSP_NMS相关配置',
            'extracted_info': {
                'has_table': False,
                'has_examples': False,
                'config_keywords': ['承载B网', 'MVSP_NMS', '接收地址'],
                'ip_operations': ['modify', 'add'],
                'ip_ranges': ['************/28', '***********/27', '***********/24']
            }
        })()
        
        with patch('app.services.config_generation.intent_service.recognize_intent_from_messages', 
                   return_value=mock_intent_response):
            
            # 执行配置生成
            response = await self.service.generate_config(request)
            
            # 验证响应
            assert response.success is True
            assert response.intent_type == IntentType.B_MVSP_NMS.value
            assert response.device_type == 'router'
            assert response.vendor == 'huawei'
            
            # 验证配置内容
            config_content = response.config_content
            assert isinstance(config_content, str)
            assert len(config_content) > 0
            
            # 验证配置包含必要的内容
            assert '#原有的配置' in config_content
            assert 'ip ip-prefix pl-MVSP_NMS_B-v4-in index 10 permit ************ 28' in config_content
            assert '#新增的配置' in config_content
            assert 'ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 27' in config_content
            assert 'ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 24' in config_content
            assert '#回收原有地址列表' in config_content
            assert 'undo ip ip-prefix pl-MVSP_NMS_B-v4-in index 10' in config_content

    @pytest.mark.asyncio
    async def test_full_pipeline_b_mvsp_nms_stream(self):
        """测试完整的B_MVSP_NMS流式配置生成流程"""
        # 创建测试请求
        messages = [
            Message(
                role="user",
                content="新增承载B网MVSP_NMS接收地址范围***********/24"
            )
        ]
        
        request = ConfigGenerationRequest(
            messages=messages,
            device_type="router",
            vendor="huawei",
            stream=True
        )
        
        # Mock意图识别结果
        mock_intent_response = type('MockResponse', (), {
            'success': True,
            'intent_type': IntentType.B_MVSP_NMS.value,
            'confidence': 0.9,
            'device_type': 'router',
            'vendor': 'huawei',
            'reasoning': '用户询问承载B网MVSP_NMS相关配置',
            'extracted_info': {
                'has_table': False,
                'has_examples': False,
                'config_keywords': ['承载B网', 'MVSP_NMS', '接收地址'],
                'ip_operations': ['add'],
                'ip_ranges': ['***********/24']
            }
        })()
        
        with patch('app.services.config_generation.intent_service.recognize_intent_from_messages', 
                   return_value=mock_intent_response):
            
            # 执行流式配置生成
            chunks = []
            async for chunk in self.service.generate_config_stream(request):
                chunks.append(chunk)
            
            # 验证流式响应
            assert len(chunks) > 0
            
            # 验证第一个chunk是开始块
            first_chunk = chunks[0]
            assert first_chunk['object'] == 'chat.completion.chunk'
            assert first_chunk['choices'][0]['delta']['role'] == 'assistant'
            
            # 验证最后一个chunk是结束块
            last_chunk = chunks[-1]
            assert last_chunk['choices'][0]['finish_reason'] == 'stop'
            
            # 合并所有内容块
            content_parts = []
            for chunk in chunks[1:-1]:  # 跳过开始和结束块
                if 'content' in chunk['choices'][0]['delta']:
                    content_parts.append(chunk['choices'][0]['delta']['content'])
            
            full_content = ''.join(content_parts)
            assert 'ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 24' in full_content

    @pytest.mark.asyncio
    async def test_cisco_device_configuration(self):
        """测试思科设备配置生成"""
        # 创建测试请求
        messages = [
            Message(
                role="user",
                content="新增承载B网MVSP_NMS接收地址范围***********/27和***********/24"
            )
        ]
        
        request = ConfigGenerationRequest(
            messages=messages,
            device_type="router",
            vendor="cisco",
            stream=False
        )
        
        # Mock意图识别结果
        mock_intent_response = type('MockResponse', (), {
            'success': True,
            'intent_type': IntentType.B_MVSP_NMS.value,
            'confidence': 0.9,
            'device_type': 'router',
            'vendor': 'cisco',
            'reasoning': '用户询问承载B网MVSP_NMS相关配置',
            'extracted_info': {
                'has_table': False,
                'has_examples': False,
                'config_keywords': ['承载B网', 'MVSP_NMS', '接收地址'],
                'ip_operations': ['add'],
                'ip_ranges': ['***********/27', '***********/24']
            }
        })()
        
        with patch('app.services.config_generation.intent_service.recognize_intent_from_messages', 
                   return_value=mock_intent_response):
            
            # 执行配置生成
            response = await self.service.generate_config(request)
            
            # 验证响应
            assert response.success is True
            assert response.vendor == 'cisco'
            
            # 验证思科配置格式
            config_content = response.config_content
            assert 'ip prefix-list MVSP_NMS_B_v4_in' in config_content
            assert '***********/27' in config_content
            assert '***********/24' in config_content

    @pytest.mark.asyncio
    async def test_intent_recognition_failure(self):
        """测试意图识别失败的情况"""
        # 创建测试请求
        messages = [
            Message(
                role="user",
                content="今天天气怎么样？"
            )
        ]
        
        request = ConfigGenerationRequest(
            messages=messages,
            device_type="router",
            vendor="huawei",
            stream=False
        )
        
        # Mock意图识别失败
        mock_intent_response = type('MockResponse', (), {
            'success': False,
            'intent_type': IntentType.INVALID.value,
            'confidence': 0.9,
            'device_type': 'unknown',
            'vendor': 'unknown',
            'reasoning': '与网络设备配置无关',
            'extracted_info': {}
        })()
        
        with patch('app.services.config_generation.intent_service.recognize_intent_from_messages', 
                   return_value=mock_intent_response):
            
            # 执行配置生成
            response = await self.service.generate_config(request)
            
            # 验证响应
            assert response.success is False
            assert response.intent_type == IntentType.INVALID.value
            assert response.code == 400

    @pytest.mark.asyncio
    async def test_error_handling_in_generation(self):
        """测试配置生成过程中的错误处理"""
        # 创建测试请求
        messages = [
            Message(
                role="user",
                content="承载B网MVSP_NMS配置"
            )
        ]
        
        request = ConfigGenerationRequest(
            messages=messages,
            device_type="router",
            vendor="huawei",
            stream=False
        )
        
        # Mock意图识别成功
        mock_intent_response = type('MockResponse', (), {
            'success': True,
            'intent_type': IntentType.B_MVSP_NMS.value,
            'confidence': 0.9,
            'device_type': 'router',
            'vendor': 'huawei',
            'reasoning': '用户询问承载B网MVSP_NMS相关配置',
            'extracted_info': {}
        })()
        
        # Mock能力执行失败
        with patch('app.services.config_generation.intent_service.recognize_intent_from_messages', 
                   return_value=mock_intent_response), \
             patch('app.core.b_mvsp_nms_capability.b_mvsp_nms_capability.generate_config_with_validation',
                   side_effect=Exception("配置生成失败")):
            
            # 执行配置生成
            response = await self.service.generate_config(request)
            
            # 验证错误响应
            assert response.success is False
            assert response.code == 500
            assert "配置生成失败" in response.message

    @pytest.mark.asyncio
    async def test_multiple_ip_operations(self):
        """测试多种IP操作的处理"""
        # 创建测试请求
        messages = [
            Message(
                role="user",
                content="""承载B网MVSP_NMS地址管理：
                1. 原地址************/28调整为***********/27
                2. 新增地址***********/24
                3. 删除地址***********/24
                
                #原有配置
                ip ip-prefix pl-MVSP_NMS_B-v4-in index 10 permit ************ 28
                ip ip-prefix pl-MVSP_NMS_B-v4-in index 20 permit *********** 24"""
            )
        ]
        
        request = ConfigGenerationRequest(
            messages=messages,
            device_type="router",
            vendor="huawei",
            stream=False
        )
        
        # Mock意图识别结果
        mock_intent_response = type('MockResponse', (), {
            'success': True,
            'intent_type': IntentType.B_MVSP_NMS.value,
            'confidence': 0.95,
            'device_type': 'router',
            'vendor': 'huawei',
            'reasoning': '用户询问承载B网MVSP_NMS相关配置',
            'extracted_info': {
                'has_table': False,
                'has_examples': False,
                'config_keywords': ['承载B网', 'MVSP_NMS'],
                'ip_operations': ['modify', 'add', 'delete'],
                'ip_ranges': ['************/28', '***********/27', '***********/24', '***********/24']
            }
        })()
        
        with patch('app.services.config_generation.intent_service.recognize_intent_from_messages', 
                   return_value=mock_intent_response):
            
            # 执行配置生成
            response = await self.service.generate_config(request)
            
            # 验证响应
            assert response.success is True
            
            # 验证配置内容包含所有操作
            config_content = response.config_content
            assert '*********** 27' in config_content  # 新地址
            assert '*********** 24' in config_content  # 新增地址
            assert 'undo ip ip-prefix pl-MVSP_NMS_B-v4-in index 10' in config_content  # 删除原地址


if __name__ == "__main__":
    pytest.main([__file__])
