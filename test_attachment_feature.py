#!/usr/bin/env python3
"""
测试新的attachment字段功能
"""

import json
from app.models.request import Message, ConfigGenerationRequest, CustomizedCapabilityRequest
from app.core.customized_capability import CustomizedCapability
from app.core.open_capability import OpenCapability
from app.core.general_capability import GeneralCapability


def test_message_with_attachment():
    """测试Message模型的attachment字段"""
    print("=== 测试Message模型的attachment字段 ===")
    
    # 测试Excel数据（列表格式）
    excel_data = [
        {"BGP AS号": "65001", "设备名": "Router1", "接口": "GE0/0/1"},
        {"BGP AS号": "65002", "设备名": "Router2", "接口": "GE0/0/2"}
    ]
    
    message_excel = Message(
        role="user",
        content="请根据这个Excel表格生成BGP配置",
        attachment=excel_data
    )
    
    print(f"Excel消息: {message_excel.dict()}")
    
    # 测试TXT数据（字符串格式）
    txt_data = "这是一个示例配置文件\ninterface GE0/0/1\n ip address *********** *************"
    
    message_txt = Message(
        role="user", 
        content="请参考这个配置文件生成类似的配置",
        attachment=txt_data
    )
    
    print(f"TXT消息: {message_txt.dict()}")
    
    # 测试没有attachment的消息（向后兼容）
    message_normal = Message(
        role="user",
        content="请生成一个基本的BGP配置"
    )
    
    print(f"普通消息: {message_normal.dict()}")
    print()


def test_customized_capability():
    """测试定制化能力的attachment处理"""
    print("=== 测试定制化能力的attachment处理 ===")
    
    capability = CustomizedCapability()
    
    # 测试Excel数据
    excel_data = [
        {"BGP AS号": "65001", "设备名": "Router1", "接口": "GE0/0/1"},
        {"BGP AS号": "65002", "设备名": "Router2", "接口": "GE0/0/2"}
    ]
    
    message_with_attachment = Message(
        role="user",
        content="请生成配置",
        attachment=excel_data
    )
    
    messages = [message_with_attachment]
    
    # 测试直接提取Excel数据
    extracted_data = capability._extract_excel_data_from_messages(messages)
    print(f"提取的Excel数据: {extracted_data}")
    
    # # 测试转换为文本格式
    # text_content = capability._extract_excel_content_from_messages(messages)
    # print(f"转换的文本内容: {text_content}")
    
    # 测试向后兼容（没有attachment字段）
    message_old_format = Message(
        role="user",
        content="BGP AS号|设备名|接口\n65001|Router1|GE0/0/1\n65002|Router2|GE0/0/2"
    )
    
    messages_old = [message_old_format]
    extracted_data_old = capability._extract_excel_data_from_messages(messages_old)
    print(f"向后兼容提取的数据: {extracted_data_old}")
    print()


def test_open_capability():
    """测试开放式能力的attachment处理"""
    print("=== 测试开放式能力的attachment处理 ===")
    
    capability = OpenCapability()
    
    # 测试TXT附件
    txt_data = "interface GE0/0/1\n ip address *********** *************\n undo shutdown"
    
    message_txt = Message(
        role="user",
        content="请参考这个配置生成类似的配置",
        attachment=txt_data
    )
    
    messages = [message_txt]
    
    # 测试提取内容
    content = capability._extract_content_from_messages(messages)
    print(f"提取的内容: {content}")
    
    # 测试提取附件数据
    attachment_data = capability._extract_attachment_data_from_messages(messages)
    print(f"提取的附件数据: {attachment_data}")
    print()


def test_general_capability():
    """测试通用能力的attachment处理"""
    print("=== 测试通用能力的attachment处理 ===")
    
    capability = GeneralCapability()
    
    # 测试带附件的问题
    excel_data = [{"配置项": "BGP", "值": "65001"}]
    
    message_with_attachment = Message(
        role="user",
        content="如何配置BGP协议？",
        attachment=excel_data
    )
    
    messages = [message_with_attachment]
    
    # 测试提取用户问题
    question = capability._extract_user_question_from_messages(messages)
    print(f"提取的用户问题: {question}")
    print()


if __name__ == "__main__":
    test_message_with_attachment()
    test_customized_capability()
    test_open_capability()
    test_general_capability()
    
    print("=== 所有测试完成 ===")
