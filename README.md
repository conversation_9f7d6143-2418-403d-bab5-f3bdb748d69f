# 数通设备配置生成智能体服务

## 项目简介

数通设备配置生成智能体是一个基于FastAPI的RESTful服务，专门用于自动化生成数通设备配置。该服务集成了意图识别、模板引擎、大语言模型和知识库检索等技术，为用户提供四种不同的配置生成能力，支持流式和非流式两种响应模式。

## 技术栈

### 核心框架
- **Python 3.12+**: 主要编程语言
- **FastAPI**: 现代、高性能的Web框架
- **Uvicorn**: ASGI服务器
- **Pydantic**: 数据验证和序列化

### 依赖库
- **httpx**: 异步HTTP客户端
- **jinja2**: 模板引擎
- **pandas**: 数据处理
- **openpyxl**: Excel文件处理
- **loguru**: 日志管理
- **tenacity**: 重试机制
- **python-dotenv**: 环境变量管理

### 开发工具
- **uv**: 包管理和虚拟环境
- **pytest**: 测试框架
- **black**: 代码格式化
- **isort**: 导入排序
- **mypy**: 类型检查

## 技术特性

- **高性能**: 基于FastAPI异步框架，支持高并发请求
- **智能识别**: 基于LLM的意图识别，准确率高，支持五种意图类型
- **模块化设计**: 清晰的继承架构，基础能力类+专用能力类，易于扩展和维护
- **双模式响应**: 支持流式(SSE)和非流式(JSON)两种响应模式
- **多厂商支持**: 支持华为、思科、华三、Juniper等主流厂商设备
- **四大核心能力**: 身份识别、通用配置、开放式配置、定制化配置
- **标准化接口**: 兼容OpenAI API格式，支持多轮对话
- **完整测试**: 系统级测试和API级测试，确保功能稳定性

## 业务逻辑

### 核心功能

1. **意图识别模块**
   - 基于LLM的智能意图识别
   - 自动提取设备类型和厂商信息
   - 支持五种意图类型：定制化、开放式、通用、身份识别、无效意图

2. **四项核心能力**
   - **身份识别能力**: 响应用户关于助手身份和功能的询问
   - **通用能力**: 基于RAG（检索增强生成）技术，结合知识库生成配置
   - **开放式能力**: 基于用户提供的示例进行few-shot学习生成配置
   - **定制化能力**: 基于预定义表格结构和Jinja2模板生成配置

3. **统一架构设计**
   - 基础能力类提供通用功能
   - LLM基础类处理大模型交互
   - 能力路由器实现智能分发
   - 流式和非流式响应支持

### 业务流程

```
用户请求 → 意图识别(LLM) → 能力路由器 → 核心能力模块 → 配置生成 → 流式/非流式响应
    ↓           ↓              ↓            ↓             ↓
消息解析    意图分类        能力选择      内容生成      格式化输出
设备识别    置信度评估      参数传递      模板渲染      SSE/JSON
```

## 架构设计

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI       │    │   意图识别       │    │   能力路由器     │
│   主服务        │───▶│   服务(LLM)     │───▶│   智能分发      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   请求处理      │    │   配置生成       │    │   四大核心能力   │
│   流式响应      │    │   服务          │    │   模块          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   工具模块      │    │   基础能力类     │    │   外部服务      │
│   日志/异常     │    │   LLM基础类     │    │   LLM/知识库    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 模块设计

```
config-agent-py-pro/
├── app/                    # 应用主目录
│   ├── config/            # 配置模块
│   │   ├── settings.py    # 应用配置
│   │   └── constants.py   # 常量定义
│   ├── core/              # 核心能力模块
│   │   ├── base_capability.py         # 基础能力类
│   │   ├── llm_based_capability.py    # LLM基础类
│   │   ├── identity_capability.py     # 身份识别能力
│   │   ├── general_capability.py      # 通用能力
│   │   ├── open_capability.py         # 开放式能力
│   │   ├── customized_capability.py   # 定制化能力
│   │   └── capability_router.py       # 能力路由器
│   ├── services/          # 服务层
│   │   ├── intent_recognition.py      # 意图识别服务
│   │   └── config_generation.py       # 配置生成服务
│   ├── utils/             # 工具模块
│   │   ├── llm_client.py             # LLM客户端
│   │   ├── knowledge_base.py         # 知识库客户端
│   │   ├── text_converter.py         # 文本转换
│   │   └── logger.py                 # 日志配置
│   ├── models/            # 数据模型
│   │   ├── request.py     # 请求模型
│   │   └── response.py    # 响应模型
│   └── main.py           # 主应用入口
├── templates/            # Jinja2模板
│   ├── default.j2        # 默认模板
│   └── huawei_router.j2  # 华为路由器模板
├── tests/               # 测试用例
├── logs/                # 日志文件
├── test_api_capabilities.py      # API能力测试
├── test_system_complete.py       # 系统完整测试
└── pyproject.toml       # 项目配置
```

## 部署设计

### Docker部署

1. **单容器部署**
```bash
# 构建镜像
docker build -t config-agent-py .

# 运行容器
docker run -p 8000:8000 config-agent-py
```

2. **Docker Compose部署**
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f config-agent
```

### 生产环境部署

1. **环境准备**
```bash
# 安装uv
pip install uv

# 创建虚拟环境
uv venv

# 激活虚拟环境
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate     # Windows

# 安装依赖
uv sync
```

2. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置
vim .env
```

3. **启动服务**
```bash
# 开发模式
uv run python start.py

# 生产模式
uv run uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

## 并发设计

### 异步处理
- 全面采用async/await异步编程模式
- 使用httpx异步HTTP客户端
- 支持流式响应处理

### 并发控制
- 配置最大并发请求数限制
- 实现请求速率限制
- 使用连接池管理外部服务连接

### 性能优化
- 启用Gzip压缩
- 实现请求缓存机制
- 优化数据库查询和外部API调用

## API接口

### 主要接口

1. **配置生成接口**
```
POST /generate-config
Content-Type: application/json

{
    "messages": [
        {"role": "user", "content": "用户输入内容"}
    ],
    "device_type": "router|switch|firewall",
    "vendor": "huawei|cisco|h3c|juniper",
    "stream": false
}
```

2. **健康检查接口**
```
GET /health
```

3. **API文档接口**
```
GET /docs          # Swagger UI
GET /redoc         # ReDoc
GET /openapi.json  # OpenAPI规范
```

### 请求格式详解

**标准请求体**:
```json
{
  "messages": [
    {
      "role": "user",
      "content": "用户输入内容"
    }
  ],
  "device_type": "router",    // 可选: router|switch|firewall
  "vendor": "huawei",         // 可选: huawei|cisco|h3c|juniper
  "stream": false             // 可选: true(流式)|false(非流式)
}
```

**响应格式**:

非流式响应 (stream=false):
```json
{
  "success": true,
  "message": "配置生成成功",
  "code": 200,
  "config_content": "生成的配置内容",
  "intent_type": "general",
  "device_type": "router",
  "vendor": "huawei"
}
```

流式响应 (stream=true):
```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"general-capability","choices":[{"index":0,"delta":{"role":"assistant"},"finish_reason":null}]}

data: {"id":"chatcmpl-124","object":"chat.completion.chunk","created":**********,"model":"general-capability","choices":[{"index":0,"delta":{"content":"配置内容"},"finish_reason":null}]}

data: {"id":"chatcmpl-125","object":"chat.completion.chunk","created":**********,"model":"general-capability","choices":[{"index":0,"delta":{"content":""},"finish_reason":"stop"}]}

data: [DONE]
```

### 响应格式

- **非流式响应**: 标准JSON格式
- **流式响应**: Server-Sent Events (SSE) 格式，兼容OpenAI标准

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| APP_NAME | 应用名称 | 数通设备配置生成智能体 |
| DEBUG | 调试模式 | false |
| HOST | 服务器主机 | 0.0.0.0 |
| PORT | 服务器端口 | 8000 |
| LLM_BASE_URL | LLM服务地址 | - |
| LLM_API_KEY | LLM API密钥 | - |
| KB_BASE_URL | 知识库服务地址 | - |

### 模板配置

支持按设备类型和厂商自定义Jinja2模板：
- `{vendor}_{device_type}.j2`: 特定厂商设备模板
- `generic_{device_type}.j2`: 通用设备类型模板
- `default.j2`: 默认模板

## 测试

### 运行测试
```bash
# 运行系统完整测试
uv run python test_system_complete.py

# 运行API能力测试
uv run python test_api_capabilities.py

# 运行单元测试
uv run pytest tests/

# 生成覆盖率报告
uv run pytest --cov=app --cov-report=html
```

### 测试覆盖
- **系统测试**：完整功能链路测试，包括模块导入、能力继承、意图识别、配置生成等
- **API测试**：四个核心能力的流式和非流式API接口测试
- **单元测试**：各模块功能单元测试
- **集成测试**：外部服务集成测试

## 核心能力详解

### 1. 身份识别能力 (Identity Capability)
**触发条件**: 用户询问助手身份、功能介绍等
**示例输入**: "你是谁？"、"你能做什么？"、"介绍一下你的功能"
**处理逻辑**: 直接返回预定义的助手介绍信息
**响应特点**: 快速响应，无需调用外部服务

### 2. 通用能力 (General Capability)
**触发条件**: 用户请求配置特定网络协议或技术
**示例输入**: "请配置BGP协议"、"如何配置OSPF"、"配置VLAN间路由"
**处理逻辑**: 调用知识库检索相关技术文档，结合LLM生成配置
**响应特点**: 基于权威技术文档，配置准确可靠

### 3. 开放式能力 (Open Capability)
**触发条件**: 用户提到"参考示例"、"参考模板"等关键词
**示例输入**: "参考示例配置VLAN"、"参考模板配置接口"
**处理逻辑**: 基于few-shot学习，参考示例生成类似配置
**响应特点**: 灵活性高，适应性强

### 4. 定制化能力 (Customized Capability)
**触发条件**: 用户输入包含表格数据或结构化信息
**示例输入**: 包含"CE端BGP AS号|设备|端口"等表格格式的数据
**处理逻辑**: 解析表格数据，使用Jinja2模板生成批量配置
**响应特点**: 批量处理，格式统一，适合大规模部署

## 监控和日志

### 日志配置
- 控制台日志：彩色格式，支持不同级别
- 文件日志：按日期轮转，自动压缩
- 错误日志：单独记录错误信息

### 健康检查
- 服务状态监控
- 运行时间统计
- 版本信息展示

## 安全考虑

### 输入验证
- 使用Pydantic进行数据验证
- 限制文件上传大小
- 防止SQL注入和XSS攻击

### 访问控制
- CORS配置
- 请求速率限制
- API密钥验证

### 数据保护
- 敏感信息脱敏
- 日志信息过滤
- 安全头部设置

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口占用
   - 验证环境变量配置
   - 查看日志文件

2. **LLM调用失败**
   - 检查网络连接
   - 验证API密钥
   - 查看超时设置

3. **模板渲染错误**
   - 检查模板语法
   - 验证数据格式
   - 查看错误日志

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 查看Docker日志
docker-compose logs -f config-agent
```

## 开发指南

### 代码规范
- 遵循PEP 8编码规范
- 使用类型注解
- 编写详细的文档字符串

### 提交规范
- 使用语义化提交信息
- 运行代码格式化工具
- 确保测试通过

### 扩展开发
- 新增核心能力模块
- 自定义模板开发
- 集成新的外部服务

## 相关文档

- [使用示例](EXAMPLES.md) - 详细的API调用示例和客户端代码
- [部署指南](DEPLOYMENT.md) - 完整的部署和运维指南
- [架构设计](ARCHITECTURE.md) - 系统架构设计和技术实现详解

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系开发团队。
