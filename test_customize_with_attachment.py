#!/usr/bin/env python3
"""
测试customize场景下直接使用attachment结构化数据
"""

import json
from app.models.request import Message, CustomizedCapabilityRequest
from app.core.customized_capability import CustomizedCapability


def test_customize_with_structured_data():
    """测试customize场景直接使用结构化数据"""
    print("=== 测试customize场景直接使用结构化数据 ===")
    
    # 模拟Excel解析后的结构化数据
    excel_data = [
        {
            "BGP AS号": "65001",
            "CE端设备": "Router1", 
            "设备型号": "NE40E",
            "接口": "GE0/0/1",
            "IP地址": "***********/30",
            "VLAN": "100"
        },
        {
            "BGP AS号": "65002",
            "CE端设备": "Router2",
            "设备型号": "NE40E", 
            "接口": "GE0/0/2",
            "IP地址": "***********/30",
            "VLAN": "200"
        },
        {
            "BGP AS号": "65003",
            "CE端设备": "Router3",
            "设备型号": "NE40E",
            "接口": "GE0/0/3", 
            "IP地址": "***********/30",
            "VLAN": "300"
        }
    ]
    
    # 创建带有attachment的消息
    message = Message(
        role="user",
        content="请根据这个Excel表格生成华为路由器的BGP配置",
        attachment=excel_data  # 直接使用结构化数据
    )
    
    # 创建customize请求
    request = CustomizedCapabilityRequest(
        messages=[message],
        device_type="router",
        vendor="huawei"
    )
    
    # 测试数据提取
    capability = CustomizedCapability()
    
    # 1. 测试直接提取结构化数据
    extracted_data = capability._extract_excel_data_from_messages(request.messages)
    print(f"提取的结构化数据: {json.dumps(extracted_data, ensure_ascii=False, indent=2)}")
    
    # 2. 验证数据类型和内容
    assert isinstance(extracted_data, list), "应该返回列表类型"
    assert len(extracted_data) == 3, "应该有3行数据"
    assert isinstance(extracted_data[0], dict), "每行应该是字典类型"
    
    # 3. 验证数据完整性
    expected_keys = {"BGP AS号", "CE端设备", "设备型号", "接口", "IP地址", "VLAN"}
    actual_keys = set(extracted_data[0].keys())
    assert actual_keys == expected_keys, f"字段不匹配: 期望{expected_keys}, 实际{actual_keys}"
    
    # 4. 测试准备生成数据
    try:
        generation_data = capability._prepare_generation_data(request)
        print(f"生成数据准备成功:")
        print(f"  - 模板名称: {generation_data['template_name']}")
        print(f"  - 数据项数量: {len(generation_data['data_items'])}")
        print(f"  - 模板数据键: {list(generation_data['template_data'].keys())}")
        
        # 验证data_items就是原始的结构化数据
        assert generation_data['data_items'] == excel_data, "data_items应该是原始的结构化数据"
        
        # 验证模板数据中包含结构化数据
        template_data = generation_data['template_data']
        assert template_data['data_items'] == excel_data, "模板数据中应该包含原始结构化数据"
        assert template_data['total_items'] == 3, "总数应该是3"
        
        print("✅ customize场景结构化数据处理正确！")
        
    except Exception as e:
        print(f"❌ 生成数据准备失败: {e}")
        return False
    
    return True


def test_customize_backward_compatibility():
    """测试customize场景的向后兼容性"""
    print("\n=== 测试customize场景向后兼容性 ===")
    
    # 创建传统格式的消息（没有attachment字段）
    message_old = Message(
        role="user",
        content="BGP AS号|CE端设备|接口\n65001|Router1|GE0/0/1\n65002|Router2|GE0/0/2"
    )
    
    request_old = CustomizedCapabilityRequest(
        messages=[message_old],
        device_type="router",
        vendor="huawei"
    )
    
    capability = CustomizedCapability()
    
    # 测试向后兼容的数据提取
    extracted_data = capability._extract_excel_data_from_messages(request_old.messages)
    print(f"向后兼容提取的数据: {json.dumps(extracted_data, ensure_ascii=False, indent=2)}")
    
    # 验证解析结果
    assert isinstance(extracted_data, list), "应该返回列表类型"
    assert len(extracted_data) == 2, "应该有2行数据"
    
    expected_keys = {"BGP AS号", "CE端设备", "接口"}
    actual_keys = set(extracted_data[0].keys())
    assert actual_keys == expected_keys, f"字段不匹配: 期望{expected_keys}, 实际{actual_keys}"
    
    print("✅ 向后兼容性正常！")
    return True


def test_customize_txt_attachment():
    """测试customize场景收到TXT attachment的处理"""
    print("\n=== 测试customize场景TXT attachment处理 ===")
    
    # 创建带有TXT attachment的消息
    txt_content = "BGP AS号|设备名|接口\n65001|Router1|GE0/0/1\n65002|Router2|GE0/0/2"
    
    message_txt = Message(
        role="user",
        content="请根据这个配置生成BGP配置",
        attachment=txt_content  # TXT格式的attachment
    )
    
    request_txt = CustomizedCapabilityRequest(
        messages=[message_txt],
        device_type="router", 
        vendor="huawei"
    )
    
    capability = CustomizedCapability()
    
    # 测试TXT attachment的处理
    extracted_data = capability._extract_excel_data_from_messages(request_txt.messages)
    print(f"TXT attachment解析的数据: {json.dumps(extracted_data, ensure_ascii=False, indent=2)}")
    
    # 验证能够正确解析TXT格式为结构化数据
    assert isinstance(extracted_data, list), "应该返回列表类型"
    assert len(extracted_data) == 2, "应该有2行数据"
    
    print("✅ TXT attachment处理正常！")
    return True


if __name__ == "__main__":
    success = True
    
    success &= test_customize_with_structured_data()
    success &= test_customize_backward_compatibility() 
    success &= test_customize_txt_attachment()
    
    if success:
        print("\n🎉 所有测试通过！customize场景能够正确处理结构化数据！")
    else:
        print("\n❌ 部分测试失败！")
