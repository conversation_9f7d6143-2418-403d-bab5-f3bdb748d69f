"""
基础能力类
提供通用的能力处理方法，减少代码重复
"""

import time
from typing import Dict, Any, AsyncGenerator, List
from abc import ABC, abstractmethod
from loguru import logger

from app.utils.llm_client import llm_client


class BaseCapability(ABC):
    """基础能力抽象类"""
    
    def __init__(self, capability_name: str):
        self.capability_name = capability_name
    
    @abstractmethod
    async def generate_config(self, request) -> str:
        """生成配置（非流式返回）- 子类必须实现"""
        pass
    
    @abstractmethod
    async def generate_config_stream(self, request) -> AsyncGenerator[Dict[str, Any], None]:
        """生成配置（流式返回）- 子类必须实现"""
        pass
    
    def _get_llm_params(self) -> Dict[str, Any]:
        """获取LLM参数"""
        return {
            "temperature": 0.1,
            # "max_tokens": 4000,
            "stream": False
        }
    
    def _get_llm_stream_params(self) -> Dict[str, Any]:
        """获取LLM流式参数"""
        params = self._get_llm_params()
        # 移除stream参数，因为chat_completion_stream方法不需要这个参数
        params.pop("stream", None)
        return params
    
    def _extract_content_from_response(self, response: Dict[str, Any]) -> str:
        """从LLM响应中提取内容"""
        try:
            if "choices" in response and len(response["choices"]) > 0:
                choice = response["choices"][0]
                if "message" in choice and "content" in choice["message"]:
                    return choice["message"]["content"].strip()
                elif "delta" in choice and "content" in choice["delta"]:
                    return choice["delta"]["content"].strip()
            
            logger.warning(f"LLM响应格式异常: {response}")
            return ""
        except Exception as e:
            logger.error(f"提取LLM响应内容失败: {str(e)}")
            return ""
    
    def _create_error_chunk(self, error_message: str) -> Dict[str, Any]:
        """创建错误响应块"""
        return {
            "id": f"chatcmpl-error-{int(time.time())}",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": self.capability_name,
            "choices": [
                {
                    "index": 0,
                    "delta": {"content": f"错误: {error_message}"},
                    "finish_reason": "stop"
                }
            ]
        }
    
    def _create_start_chunk(self, content: str = None) -> Dict[str, Any]:
        """创建开始响应块"""
        return {
            "id": f"chatcmpl-start-{int(time.time())}",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": self.capability_name,
            "choices": [
                {
                    "index": 0,
                    "delta": {
                        "role": "assistant",
                        "content": content or f"正在使用{self.capability_name}生成配置..."
                    },
                    "finish_reason": None
                }
            ]
        }
    
    def _create_end_chunk(self) -> Dict[str, Any]:
        """创建结束响应块"""
        return {
            "id": f"chatcmpl-end-{int(time.time())}",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": self.capability_name,
            "choices": [
                {
                    "index": 0,
                    "delta": {},
                    "finish_reason": "stop"
                }
            ]
        }
    
    async def _create_content_chunks(self, content: str, chunk_size: int = 100) -> AsyncGenerator[Dict[str, Any], None]:
        """将内容分块为流式响应"""
        import asyncio
        
        # 发送开始块
        yield self._create_start_chunk()
        await asyncio.sleep(0.01)
        
        # 按行分割内容
        lines = content.split('\n')
        current_chunk = ""
        
        for i, line in enumerate(lines):
            current_chunk += line + '\n'
            
            # 当累积内容达到chunk_size或到达最后一行时发送
            if len(current_chunk) >= chunk_size or i == len(lines) - 1:
                if current_chunk.strip():
                    chunk = {
                        "id": f"chatcmpl-content-{i}",
                        "object": "chat.completion.chunk",
                        "created": int(time.time()),
                        "model": self.capability_name,
                        "choices": [
                            {
                                "index": 0,
                                "delta": {"content": current_chunk},
                                "finish_reason": None
                            }
                        ]
                    }
                    yield chunk
                    current_chunk = ""
                    await asyncio.sleep(0.01)
        
        # 发送结束块
        yield self._create_end_chunk()
    
    def _validate_request_basic(self, request) -> bool:
        """基础请求验证"""
        if not hasattr(request, 'messages') or not request.messages:
            raise ValueError("消息列表不能为空")
        
        if not hasattr(request, 'device_type') or not request.device_type.strip():
            raise ValueError("设备类型不能为空")
        
        if not hasattr(request, 'vendor') or not request.vendor.strip():
            raise ValueError("厂商信息不能为空")
        
        return True
    
    async def generate_config_with_validation(self, request, stream: bool = False):
        """带验证的配置生成"""
        try:
            # 基础验证
            self._validate_request_basic(request)
            
            # 调用相应的生成方法
            if stream:
                return self.generate_config_stream(request)
            else:
                return await self.generate_config(request)
                
        except ValueError as e:
            logger.error(f"{self.capability_name}请求验证失败: {str(e)}")
            if stream:
                async def error_stream():
                    yield self._create_error_chunk(str(e))
                return error_stream()
            else:
                raise
        except Exception as e:
            logger.error(f"{self.capability_name}配置生成失败: {str(e)}")
            if stream:
                async def error_stream():
                    yield self._create_error_chunk(f"配置生成失败: {str(e)}")
                return error_stream()
            else:
                raise


class LLMBasedCapability(BaseCapability):
    """基于LLM的能力基类"""
    
    def __init__(self, capability_name: str):
        super().__init__(capability_name)
    
    async def _call_llm(self, messages: List[Dict[str, str]], stream: bool = False) -> Dict[str, Any]:
        """调用LLM"""
        params = self._get_llm_stream_params() if stream else self._get_llm_params()
        return await llm_client.chat_completion(messages, **params)
    
    async def _call_llm_stream(self, messages: List[Dict[str, str]]) -> AsyncGenerator[Dict[str, Any], None]:
        """调用LLM流式接口"""
        params = self._get_llm_stream_params()
        async for chunk in llm_client.chat_completion_stream(messages, **params):
            yield chunk
    
    def _prepare_llm_messages(self, request) -> List[Dict[str, str]]:
        """准备LLM消息 - 子类可以重写"""
        if hasattr(request, 'messages') and request.messages:
            return [{"role": msg.role, "content": msg.content} for msg in request.messages]
        else:
            raise ValueError("无法从请求中提取消息")
