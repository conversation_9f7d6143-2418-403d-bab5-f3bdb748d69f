"""
B_MVSP_NMS能力单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock

from app.core.b_mvsp_nms_capability import BMVSPNMSCapability
from app.models.request import ConfigGenerationRequest, Message


class TestBMVSPNMSCapability:
    """B_MVSP_NMS能力测试类"""

    def setup_method(self):
        """测试前准备"""
        self.capability = BMVSPNMSCapability()

    def test_extract_ip_operations_basic(self):
        """测试基本IP操作提取"""
        user_input = """
        因集团IPTV业务需求，需新联通云中原O域资源池通过承载B MVSP_NMS VPN与各省对接，申请资源池承载B网MVSP_NMS接收地址需求如下： 
        原接收地址范围由************/28调整为***********/27（河南）；
        新增接收地址范围***********/24（黑龙江）；
        """
        
        operations = self.capability._extract_ip_operations(user_input)
        
        # 验证提取的操作数量
        assert len(operations) == 3
        
        # 验证操作类型和IP地址
        ip_addresses = [op['cidr'] for op in operations]
        assert '************/28' in ip_addresses
        assert '***********/27' in ip_addresses
        assert '***********/24' in ip_addresses

    def test_extract_ip_operations_with_spaces(self):
        """测试带空格的IP地址格式提取"""
        user_input = """
        ip ip-prefix pl-MVSP_NMS_B-v4-in index 10 permit ************ 28
        新增地址: *********** 24
        """
        
        operations = self.capability._extract_ip_operations(user_input)
        
        # 验证能够正确提取带空格的IP地址格式
        ip_addresses = [op['cidr'] for op in operations]
        assert '************/28' in ip_addresses
        assert '***********/24' in ip_addresses

    def test_extract_existing_config_info(self):
        """测试现有配置信息提取"""
        user_input = """
        #原有的配置
        ip ip-prefix pl-MVSP_NMS_B-v4-in index 10 permit ************ 28
        ip ip-prefix pl-MVSP_NMS_B-v4-in index 20 permit *********** 24
        """
        
        existing_configs = self.capability._extract_existing_config_info(user_input)
        
        # 验证提取的配置数量和内容
        assert len(existing_configs) == 2
        
        config1 = existing_configs[0]
        assert config1['index'] == 10
        assert config1['ip'] == '************'
        assert config1['mask'] == 28
        assert config1['cidr'] == '************/28'
        
        config2 = existing_configs[1]
        assert config2['index'] == 20
        assert config2['ip'] == '***********'
        assert config2['mask'] == 24

    def test_generate_huawei_config(self):
        """测试华为设备配置生成"""
        operations = [
            {'type': 'modify_old', 'ip': '************', 'mask': 28, 'cidr': '************/28'},
            {'type': 'modify_new', 'ip': '***********', 'mask': 27, 'cidr': '***********/27'},
            {'type': 'add', 'ip': '***********', 'mask': 24, 'cidr': '***********/24'}
        ]
        
        existing_configs = [
            {'index': 10, 'ip': '************', 'mask': 28, 'cidr': '************/28'}
        ]
        
        config = self.capability._generate_huawei_config(operations, existing_configs)
        
        # 验证生成的配置包含必要的内容
        assert '#原有的配置' in config
        assert 'ip ip-prefix pl-MVSP_NMS_B-v4-in index 10 permit ************ 28' in config
        assert '#新增的配置' in config
        assert 'ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 27' in config
        assert 'ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 24' in config
        assert '#回收原有地址列表' in config
        assert 'undo ip ip-prefix pl-MVSP_NMS_B-v4-in index 10' in config

    def test_generate_cisco_config(self):
        """测试思科设备配置生成"""
        operations = [
            {'type': 'add', 'ip': '***********', 'mask': 27, 'cidr': '***********/27'},
            {'type': 'add', 'ip': '***********', 'mask': 24, 'cidr': '***********/24'}
        ]
        
        existing_configs = []
        
        config = self.capability._generate_cisco_config(operations, existing_configs)
        
        # 验证生成的配置包含必要的内容
        assert '! Original configuration' in config
        assert 'ip prefix-list MVSP_NMS_B_v4_in seq 10 permit ***********/27' in config
        assert 'ip prefix-list MVSP_NMS_B_v4_in seq 20 permit ***********/24' in config

    @pytest.mark.asyncio
    async def test_generate_config_huawei(self):
        """测试华为设备配置生成（完整流程）"""
        # 创建测试请求
        messages = [
            Message(
                role="user",
                content="""因集团IPTV业务需求，需新联通云中原O域资源池通过承载B MVSP_NMS VPN与各省对接，申请资源池承载B网MVSP_NMS接收地址需求如下： 
                原接收地址范围由************/28调整为***********/27（河南）；
                新增接收地址范围***********/24（黑龙江）；
                
                #原有的配置
                ip ip-prefix pl-MVSP_NMS_B-v4-in index 10 permit ************ 28"""
            )
        ]
        
        request = ConfigGenerationRequest(
            messages=messages,
            device_type="router",
            vendor="huawei"
        )
        
        # 生成配置
        config = await self.capability.generate_config(request)
        
        # 验证配置内容
        assert isinstance(config, str)
        assert len(config) > 0
        assert 'ip ip-prefix pl-MVSP_NMS_B-v4-in' in config

    @pytest.mark.asyncio
    async def test_generate_config_cisco(self):
        """测试思科设备配置生成（完整流程）"""
        # 创建测试请求
        messages = [
            Message(
                role="user",
                content="""新增接收地址范围***********/27和***********/24"""
            )
        ]
        
        request = ConfigGenerationRequest(
            messages=messages,
            device_type="router",
            vendor="cisco"
        )
        
        # 生成配置
        config = await self.capability.generate_config(request)
        
        # 验证配置内容
        assert isinstance(config, str)
        assert len(config) > 0
        assert 'ip prefix-list MVSP_NMS_B_v4_in' in config

    @pytest.mark.asyncio
    async def test_generate_config_stream(self):
        """测试流式配置生成"""
        # 创建测试请求
        messages = [
            Message(
                role="user",
                content="新增接收地址范围***********/24"
            )
        ]
        
        request = ConfigGenerationRequest(
            messages=messages,
            device_type="router",
            vendor="huawei"
        )
        
        # 生成流式配置
        chunks = []
        async for chunk in self.capability.generate_config_stream(request):
            chunks.append(chunk)
        
        # 验证流式响应
        assert len(chunks) > 0
        assert chunks[0]['object'] == 'chat.completion.chunk'
        assert chunks[-1]['choices'][0]['finish_reason'] == 'stop'

    def test_extract_user_input_from_messages(self):
        """测试从消息中提取用户输入"""
        messages = [
            Message(role="system", content="系统消息"),
            Message(role="user", content="用户消息1"),
            Message(role="assistant", content="助手回复"),
            Message(role="user", content="最新用户消息")
        ]
        
        user_input = self.capability._extract_user_input_from_messages(messages)
        
        # 应该返回最新的用户消息
        assert user_input == "最新用户消息"

    def test_extract_user_input_with_attachment(self):
        """测试从带附件的消息中提取用户输入"""
        messages = [
            Message(
                role="user", 
                content="请处理附件内容",
                attachment="附件中的IP地址配置信息"
            )
        ]
        
        user_input = self.capability._extract_user_input_from_messages(messages)
        
        # 应该优先返回附件内容
        assert user_input == "附件中的IP地址配置信息"

    def test_invalid_ip_address_handling(self):
        """测试无效IP地址的处理"""
        user_input = "无效IP地址: 999.999.999.999/99"
        
        operations = self.capability._extract_ip_operations(user_input)
        
        # 无效IP地址应该被过滤掉
        assert len(operations) == 0


if __name__ == "__main__":
    pytest.main([__file__])
