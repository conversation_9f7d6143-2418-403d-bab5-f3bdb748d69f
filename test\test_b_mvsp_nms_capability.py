"""
B_MVSP_NMS能力单元测试 - 简化版
"""

import pytest
import asyncio
from unittest.mock import Mock, patch

from app.core.b_mvsp_nms_capability import BMVSPNMSCapability
from app.models.request import ConfigGenerationRequest, Message


class TestBMVSPNMSCapability:
    """B_MVSP_NMS能力测试类"""

    def setup_method(self):
        """测试前准备"""
        self.capability = BMVSPNMSCapability()

    def test_capability_initialization(self):
        """测试能力类初始化"""
        assert self.capability.capability_name == "b-mvsp-nms-capability"
        assert hasattr(self.capability, 'prompt_template')
        assert len(self.capability.prompt_template) > 0

    def test_extract_user_input_from_messages(self):
        """测试从消息中提取用户输入"""
        messages = [
            Message(role="system", content="系统消息"),
            Message(role="user", content="用户消息1"),
            Message(role="assistant", content="助手回复"),
            Message(role="user", content="最新用户消息")
        ]
        
        user_input = self.capability._extract_user_input_from_messages(messages)
        
        # 应该返回最新的用户消息
        assert user_input == "最新用户消息"

    @pytest.mark.asyncio
    async def test_generate_config_with_llm(self):
        """测试LLM配置生成"""
        # Mock LLM响应
        mock_response = {
            "choices": [{
                "message": {
                    "content": """#新增的配置
ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 27
#"""
                }
            }]
        }
        
        with patch.object(self.capability, '_call_llm', return_value=mock_response):
            config = await self.capability._generate_config_with_llm(
                "新增承载B网MVSP_NMS接收地址", "router", "huawei"
            )
            
            assert isinstance(config, str)
            assert "ip ip-prefix pl-MVSP_NMS_B-v4-in" in config

    @pytest.mark.asyncio
    async def test_generate_config_huawei(self):
        """测试华为设备配置生成（完整流程）"""
        # 创建测试请求
        messages = [
            Message(
                role="user",
                content="原接收地址范围调整和新增地址范围"
            )
        ]
        
        request = ConfigGenerationRequest(
            messages=messages,
            device_type="router",
            vendor="huawei"
        )
        
        # Mock LLM响应
        mock_response = {
            "choices": [{
                "message": {
                    "content": """#新增的配置
ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 27
#"""
                }
            }]
        }
        
        with patch.object(self.capability, '_call_llm', return_value=mock_response):
            config = await self.capability.generate_config(request)
            
            # 验证配置内容
            assert isinstance(config, str)
            assert len(config) > 0

    @pytest.mark.asyncio
    async def test_llm_error_handling(self):
        """测试LLM错误处理"""
        with patch.object(self.capability, '_call_llm', side_effect=Exception("LLM调用失败")):
            config = await self.capability._generate_config_with_llm(
                "测试输入", "router", "huawei"
            )
            
            assert "配置生成失败" in config
            assert "LLM调用失败" in config


if __name__ == "__main__":
    pytest.main([__file__])
