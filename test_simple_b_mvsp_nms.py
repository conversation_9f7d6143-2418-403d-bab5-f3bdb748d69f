#!/usr/bin/env python3
"""
简单的B_MVSP_NMS功能测试
"""

import asyncio
from unittest.mock import Mock, patch
from app.core.b_mvsp_nms_capability import BMVSPNMSCapability
from app.models.request import ConfigGenerationRequest, Message


async def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("B_MVSP_NMS基本功能测试")
    print("=" * 60)
    
    capability = BMVSPNMSCapability()
    
    # 测试用户输入提取
    messages = [
        Message(role="user", content="承载B网MVSP_NMS接收地址调整需求")
    ]
    
    user_input = capability._extract_user_input_from_messages(messages)
    print(f"提取的用户输入: {user_input}")
    
    # 测试提示词构建
    prompt = capability.prompt_template.format(
        user_input=user_input,
        device_type="router",
        vendor="huawei"
    )
    
    print(f"\n构建的提示词长度: {len(prompt)} 字符")
    print(f"提示词预览: {prompt[:200]}...")


async def test_with_mock_llm():
    """使用Mock LLM测试配置生成"""
    print("\n" + "=" * 60)
    print("Mock LLM配置生成测试")
    print("=" * 60)
    
    capability = BMVSPNMSCapability()
    
    # Mock LLM响应
    mock_response = {
        "choices": [{
            "message": {
                "content": """#原有的配置
ip ip-prefix pl-MVSP_NMS_B-v4-in index 10 permit ************ 28 

#新增的配置
ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 27
ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 24
#回收原有地址列表
undo ip ip-prefix pl-MVSP_NMS_B-v4-in index 10
#"""
            }
        }]
    }
    
    # 创建测试请求
    messages = [
        Message(
            role="user",
            content="原接收地址范围由************/28调整为***********/27；新增接收地址范围***********/24"
        )
    ]
    
    request = ConfigGenerationRequest(
        messages=messages,
        device_type="router",
        vendor="huawei"
    )
    
    # Mock LLM调用
    with patch.object(capability, '_call_llm', return_value=mock_response):
        config = await capability.generate_config(request)
        print(f"生成的配置:\n{config}")


async def test_stream_with_mock():
    """测试流式响应"""
    print("\n" + "=" * 60)
    print("流式响应测试")
    print("=" * 60)
    
    capability = BMVSPNMSCapability()
    
    # Mock流式响应
    async def mock_stream():
        yield {
            "id": "chatcmpl-1",
            "object": "chat.completion.chunk",
            "choices": [{
                "index": 0,
                "delta": {"role": "assistant", "content": "#原有的配置\n"},
                "finish_reason": None
            }]
        }
        yield {
            "id": "chatcmpl-2", 
            "object": "chat.completion.chunk",
            "choices": [{
                "index": 0,
                "delta": {"content": "ip ip-prefix pl-MVSP_NMS_B-v4-in permit *********** 27\n"},
                "finish_reason": None
            }]
        }
        yield {
            "id": "chatcmpl-3",
            "object": "chat.completion.chunk", 
            "choices": [{
                "index": 0,
                "delta": {},
                "finish_reason": "stop"
            }]
        }
    
    messages = [Message(role="user", content="新增承载B网MVSP_NMS接收地址")]
    request = ConfigGenerationRequest(messages=messages, device_type="router", vendor="huawei")
    
    # Mock流式LLM调用
    with patch.object(capability, '_call_llm_stream', return_value=mock_stream()):
        print("流式响应:")
        chunk_count = 0
        async for chunk in capability.generate_config_stream(request):
            chunk_count += 1
            print(f"Chunk {chunk_count}: {chunk}")


async def main():
    """主测试函数"""
    print("B_MVSP_NMS简化实现测试")
    print("=" * 60)
    
    try:
        await test_basic_functionality()
        await test_with_mock_llm()
        await test_stream_with_mock()
        
        print("\n" + "=" * 60)
        print("测试完成！新的简化实现工作正常。")
        print("实际使用时需要配置LLM服务。")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
