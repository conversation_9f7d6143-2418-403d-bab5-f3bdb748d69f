"""
常量定义模块
"""

from enum import Enum


class IntentType(Enum):
    """意图类型枚举"""
    CUSTOMIZED = "customized"  # 定制化能力
    OPEN = "open"  # 开放式能力
    GENERAL = "general"  # 通用能力
    IDENTITY = "identity"  # 身份识别能力
    B_MVSP_NMS = "b_mvsp_nms"  # 承载B网MVSP_NMS配置能力
    INVALID = "invalid"  # 无效意图


class DeviceType(Enum):
    """设备类型枚举"""
    ROUTER = "router"  # 路由器
    SWITCH = "switch"  # 交换机
    FIREWALL = "firewall"  # 防火墙
    UNKNOWN = "unknown"  # 未知设备


class VendorType(Enum):
    """厂商类型枚举"""
    HUAWEI = "huawei"  # 华为
    CISCO = "cisco"  # 思科
    H3C = "h3c"  # 华三
    JUNIPER = "juniper"  # 瞻博
    UNKNOWN = "unknown"  # 未知厂商


# 定制化能力表头关键词
CUSTOMIZED_TABLE_HEADERS = [
    "CE端BGP AS号", "CE端设备", "设备型号", "CE端口", "CE端互联IP地址",
    "VLAN", "BFD时延", "vpn-instance", "rt/rd", "接收地址范围",
    "发送地址范围", "AS号", "终端设备", "终端端口", "ER互联IP地址",
    "互联带宽", "VRF name", "vpn申请人", "联系方式", "备注"
]

# B_MVSP_NMS能力关键词
B_MVSP_NMS_KEYWORDS = [
    "承载B网", "MVSP_NMS", "mvsp_nms", "接收地址", "发送地址",
    "地址范围", "ip-prefix", "pl-MVSP_NMS", "调整", "新增", "删除",
    "原接收地址", "新增接收地址", "回收", "undo"
]

# 设备配置无关问题的回复
INVALID_QUESTION_RESPONSE = "对不起，请提问与配置生成相关的问题"

# 文件类型
SUPPORTED_FILE_TYPES = {
    ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    ".xls": "application/vnd.ms-excel",
    ".txt": "text/plain",
    ".csv": "text/csv"
}

# HTTP状态码
HTTP_STATUS = {
    "SUCCESS": 200,
    "BAD_REQUEST": 400,
    "UNAUTHORIZED": 401,
    "FORBIDDEN": 403,
    "NOT_FOUND": 404,
    "INTERNAL_ERROR": 500,
    "SERVICE_UNAVAILABLE": 503
}

# 日志级别
LOG_LEVELS = {
    "DEBUG": "DEBUG",
    "INFO": "INFO",
    "WARNING": "WARNING",
    "ERROR": "ERROR",
    "CRITICAL": "CRITICAL"
}

# 提示词模板
PROMPT_TEMPLATES = {
    "OPEN_CAPABILITY": """你是数通设备配置助手，用户上传了excel：{excel_content}，txt：{txt_content}。
数通设备配置会通过excel的内容生成txt的结果，请学习之后模仿该方式，根据实际的需求excel：{requirement_excel}，返回配置代码。
设备类型：{device_type}，厂商：{vendor}""",
    
    "GENERAL_CAPABILITY": """你是数通设备配置助手，请根据用户的问题和参考文档生成配置代码。
用户问题：{user_question}
参考文档：{reference_docs}
设备类型：{device_type}，厂商：{vendor}
如果用户的问题比较简单，你在生成配置代码时，根据参考文档只生成必要的内容，但是可以给予用户一些可选的配置选项。以下是一个例子：
####例子
用户问题是：请配置一个以太网汇聚接口配置。
回答：interface Eth-Trunk1
 description "To-[PEER-DEVICE]-eth-trunk1"  # 描述对端设备信息
 mode lacp-static  # 配置为LACP静态模式
 portswitch  # 默认为二层接口，如需三层接口则使用"undo portswitch"
 
# 可选配置（根据实际需求添加）：
#  peer-link 1  # 如果作为M-LAG对等链路则启用
#  stp edged-port enable  # 启用STP边缘端口功能
#  dfs-group 1 m-lag 1  # 加入DFS组和M-LAG
#  port nvo3 mode access  # 配置为NVO3接入模式
#  ip address *********** *************  # 如果是三层接口则配置IP地址
"""
}

# 正则表达式模式
REGEX_PATTERNS = {
    "IP_ADDRESS": r"\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b",
    "VLAN_ID": r"\bvlan\s+(\d+)\b",
    "AS_NUMBER": r"\bas\s+(\d+)\b",
    "DEVICE_NAME": r"^[a-zA-Z0-9\-_]+$"
}

# 缓存配置
CACHE_CONFIG = {
    "TTL": 3600,  # 缓存过期时间(秒)
    "MAX_SIZE": 1000,  # 最大缓存条目数
    "CLEANUP_INTERVAL": 300  # 清理间隔(秒)
}
