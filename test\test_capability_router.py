"""
能力路由器单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock, patch

from app.services.config_generation import CapabilityRouter
from app.models.request import ConfigGenerationRequest, Message
from app.config.constants import IntentType


class TestCapabilityRouter:
    """能力路由器测试类"""

    def setup_method(self):
        """测试前准备"""
        self.router = CapabilityRouter()

    def test_capability_mapping_completeness(self):
        """测试能力映射表的完整性"""
        # 验证所有意图类型都有对应的能力映射
        expected_intents = [
            IntentType.CUSTOMIZED.value,
            IntentType.OPEN.value,
            IntentType.GENERAL.value,
            IntentType.IDENTITY.value,
            IntentType.B_MVSP_NMS.value
        ]
        
        for intent in expected_intents:
            assert intent in self.router.capability_mapping, f"缺少意图类型映射: {intent}"
            
            mapping = self.router.capability_mapping[intent]
            assert 'capability' in mapping, f"意图类型 {intent} 缺少capability字段"
            assert 'request_class' in mapping, f"意图类型 {intent} 缺少request_class字段"

    def test_create_capability_request_b_mvsp_nms(self):
        """测试创建B_MVSP_NMS能力请求"""
        context = {
            'messages': [Message(role="user", content="承载B网MVSP_NMS配置")],
            'device_type': 'router',
            'vendor': 'huawei'
        }
        
        request = self.router._create_capability_request(IntentType.B_MVSP_NMS.value, context)
        
        # 验证请求对象
        assert isinstance(request, ConfigGenerationRequest)
        assert len(request.messages) == 1
        assert request.messages[0].content == "承载B网MVSP_NMS配置"
        assert request.device_type == 'router'
        assert request.vendor == 'huawei'

    def test_create_capability_request_invalid_intent(self):
        """测试创建无效意图的能力请求"""
        context = {
            'messages': [Message(role="user", content="测试消息")],
            'device_type': 'router',
            'vendor': 'huawei'
        }
        
        with pytest.raises(ValueError):
            self.router._create_capability_request("invalid_intent", context)

    @pytest.mark.asyncio
    async def test_route_to_b_mvsp_nms_capability(self):
        """测试路由到B_MVSP_NMS能力"""
        # 创建测试请求
        messages = [Message(role="user", content="承载B网MVSP_NMS接收地址调整")]
        request = ConfigGenerationRequest(
            messages=messages,
            device_type="router",
            vendor="huawei"
        )
        
        # Mock B_MVSP_NMS能力的响应
        mock_capability = Mock()
        mock_capability.generate_config_with_validation.return_value = "生成的配置内容"
        
        # 替换路由器中的能力实例
        original_capability = self.router.capability_mapping[IntentType.B_MVSP_NMS.value]['capability']
        self.router.capability_mapping[IntentType.B_MVSP_NMS.value]['capability'] = mock_capability
        
        try:
            # 执行路由
            result = await self.router.route_to_capability(IntentType.B_MVSP_NMS.value, request, stream=False)
            
            # 验证结果
            assert result == "生成的配置内容"
            mock_capability.generate_config_with_validation.assert_called_once()
            
        finally:
            # 恢复原始能力实例
            self.router.capability_mapping[IntentType.B_MVSP_NMS.value]['capability'] = original_capability

    @pytest.mark.asyncio
    async def test_route_to_b_mvsp_nms_capability_stream(self):
        """测试流式路由到B_MVSP_NMS能力"""
        # 创建测试请求
        messages = [Message(role="user", content="承载B网MVSP_NMS接收地址调整")]
        request = ConfigGenerationRequest(
            messages=messages,
            device_type="router",
            vendor="huawei"
        )
        
        # Mock B_MVSP_NMS能力的流式响应
        async def mock_stream():
            yield {"chunk": "配置内容1"}
            yield {"chunk": "配置内容2"}
        
        mock_capability = Mock()
        mock_capability.generate_config_with_validation.return_value = mock_stream()
        
        # 替换路由器中的能力实例
        original_capability = self.router.capability_mapping[IntentType.B_MVSP_NMS.value]['capability']
        self.router.capability_mapping[IntentType.B_MVSP_NMS.value]['capability'] = mock_capability
        
        try:
            # 执行流式路由
            result_stream = await self.router.route_to_capability(IntentType.B_MVSP_NMS.value, request, stream=True)
            
            # 收集流式结果
            chunks = []
            async for chunk in result_stream:
                chunks.append(chunk)
            
            # 验证结果
            assert len(chunks) == 2
            assert chunks[0]["chunk"] == "配置内容1"
            assert chunks[1]["chunk"] == "配置内容2"
            
        finally:
            # 恢复原始能力实例
            self.router.capability_mapping[IntentType.B_MVSP_NMS.value]['capability'] = original_capability

    def test_get_capability_info(self):
        """测试获取能力信息"""
        # 测试获取B_MVSP_NMS能力信息
        capability_info = self.router.get_capability_info(IntentType.B_MVSP_NMS.value)
        
        assert capability_info is not None
        assert 'capability' in capability_info
        assert 'request_class' in capability_info

    def test_get_capability_info_invalid(self):
        """测试获取无效能力信息"""
        capability_info = self.router.get_capability_info("invalid_intent")
        
        assert capability_info is None

    def test_list_available_capabilities(self):
        """测试列出可用能力"""
        capabilities = self.router.list_available_capabilities()
        
        # 验证包含所有预期的能力类型
        expected_capabilities = [
            IntentType.CUSTOMIZED.value,
            IntentType.OPEN.value,
            IntentType.GENERAL.value,
            IntentType.IDENTITY.value,
            IntentType.B_MVSP_NMS.value
        ]
        
        for capability in expected_capabilities:
            assert capability in capabilities

    @pytest.mark.asyncio
    async def test_route_with_invalid_intent(self):
        """测试使用无效意图进行路由"""
        messages = [Message(role="user", content="测试消息")]
        request = ConfigGenerationRequest(
            messages=messages,
            device_type="router",
            vendor="huawei"
        )
        
        with pytest.raises(ValueError):
            await self.router.route_to_capability("invalid_intent", request, stream=False)

    def test_capability_instances_are_different(self):
        """测试不同能力实例是独立的"""
        b_mvsp_nms_capability = self.router.capability_mapping[IntentType.B_MVSP_NMS.value]['capability']
        general_capability = self.router.capability_mapping[IntentType.GENERAL.value]['capability']
        
        # 验证是不同的实例
        assert b_mvsp_nms_capability is not general_capability
        assert type(b_mvsp_nms_capability).__name__ == 'BMVSPNMSCapability'

    @pytest.mark.asyncio
    async def test_error_handling_in_routing(self):
        """测试路由过程中的错误处理"""
        # 创建测试请求
        messages = [Message(role="user", content="测试消息")]
        request = ConfigGenerationRequest(
            messages=messages,
            device_type="router",
            vendor="huawei"
        )
        
        # Mock能力抛出异常
        mock_capability = Mock()
        mock_capability.generate_config_with_validation.side_effect = Exception("能力执行失败")
        
        # 替换路由器中的能力实例
        original_capability = self.router.capability_mapping[IntentType.B_MVSP_NMS.value]['capability']
        self.router.capability_mapping[IntentType.B_MVSP_NMS.value]['capability'] = mock_capability
        
        try:
            # 执行路由，应该抛出异常
            with pytest.raises(Exception, match="能力执行失败"):
                await self.router.route_to_capability(IntentType.B_MVSP_NMS.value, request, stream=False)
                
        finally:
            # 恢复原始能力实例
            self.router.capability_mapping[IntentType.B_MVSP_NMS.value]['capability'] = original_capability


if __name__ == "__main__":
    pytest.main([__file__])
