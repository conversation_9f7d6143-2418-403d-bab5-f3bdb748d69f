{% macro bgp_neighbor(ip, as_number, description, import_filter, export_filter) %}
! BGP Neighbor: {{ ip }}
peer {{ ip }} as-number {{ as_number }}
peer {{ ip }} description {{ description }}
peer {{ ip }} bfd enable
peer {{ ip }} bfd min-tx-interval {{ bfd_interval }} min-rx-interval {{ bfd_interval }}
{% if import_filter %}
peer {{ ip }} ip-prefix {{ import_filter }} import
{% endif %}
{% if export_filter %}
peer {{ ip }} ip-prefix {{ export_filter }} export
{% endif %}
{% endmacro %}


! 创建 VRF / VPN 实例
ip vpn-instance {{ vpn_instance }}
 ipv4-family
  route-distinguisher {{ rd_rt }}
  apply-label per-instance
  vpn-target {{ rd_rt }} export-extcommunity
  vpn-target {{ rd_rt }} import-extcommunity

! 与 防火墙（FW）互联的子接口
interface {{ fw_interface }}.{{ vlan }}
 vlan-type dot1q {{ vlan }}
 description To-FW-{{ fw_interface }}
 ip binding vpn-instance {{ vpn_instance }}
 ip address {{ fw_ip }} {{ fw_mask }}
 statistic enable

! 与 ER 互联的子接口
interface {{ er_interface }}.{{ vlan }}
 vlan-type dot1q {{ vlan }}
 description To-{{ er_name }}-{{ er_interface }}
 ip binding vpn-instance {{ vpn_instance }}
 ip address {{ er_ip }} {{ er_mask }}
 statistic enable



! 路由前缀列表
! ——————————————————————

! 接收前缀列表
ip ip-prefix pl_{{ vpn_instance }}_in index 10 permit {{ in_prefix }} greater-equal 24 less-equal 32

! 发送前缀列表
ip ip-prefix pl_{{ vpn_instance }}_out index 10 permit {{ out_prefix }}

! BGP 配置
bgp {{ bgp_as }}
 ipv4-family vpn-instance {{ vpn_instance }}
 {% for peer in bgp_peers -%}
 {% if peer.description is defined and peer.as_number is defined -%}
 peer {{ peer.ip }} as-number {{ peer.as_number }}
 peer {{ peer.ip }} description {{ peer.description }}
 peer {{ peer.ip }} bfd enable
 peer {{ peer.ip }} bfd min-tx-interval {{ bfd_interval }} min-rx-interval {{ bfd_interval }}
 {% if peer.import_filter -%}
 peer {{ peer.ip }} ip-prefix {{ peer.import_filter }} import
 {% endif -%}
 {% if peer.export_filter -%}
 peer {{ peer.ip }} ip-prefix {{ peer.export_filter }} export
 {% endif -%}
 {% if peer.next_hop_local -%}
 peer {{ peer.ip }} next-hop-local
 {% endif -%}
 {% endif -%}
 {% endfor -%}
! {{ ce_name }} 配置结束
