"""
配置生成服务
整合意图识别和三项核心能力
"""

import json
from typing import Dict, Any, AsyncGenerator, Union
from loguru import logger

from app.config.constants import IntentType, INVALID_QUESTION_RESPONSE
from app.models.request import (
    ConfigGenerationRequest, CustomizedCapabilityRequest,
    OpenCapabilityRequest, GeneralCapabilityRequest, IntentRecognitionRequest
)
from app.models.response import ConfigGenerationResponse
from app.services.intent_recognition import intent_service
from app.core.customized_capability import customized_capability
from app.core.open_capability import open_capability
from app.core.general_capability import general_capability
from app.core.identity_capability import identity_capability


class CapabilityRouter:
    """能力路由器 - 统一处理路由逻辑"""

    def __init__(self):
        # 能力映射表
        self.capability_mapping = {
            IntentType.CUSTOMIZED.value: {
                'capability': customized_capability,
                'request_class': CustomizedCapabilityRequest
            },
            IntentType.OPEN.value: {
                'capability': open_capability,
                'request_class': OpenCapabilityRequest
            },
            IntentType.GENERAL.value: {
                'capability': general_capability,
                'request_class': GeneralCapabilityRequest
            },
            IntentType.IDENTITY.value: {
                'capability': identity_capability,
                'request_class': ConfigGenerationRequest
            }
        }

    def _create_capability_request(self, intent_type: str, context: Dict[str, Any]):
        """创建能力请求对象"""
        if intent_type not in self.capability_mapping:
            raise ValueError(INVALID_QUESTION_RESPONSE)

        mapping = self.capability_mapping[intent_type]
        request_class = mapping['request_class']

        # 构建请求参数
        request_params = {
            'messages': context['messages'],
            'device_type': context['device_type'],
            'vendor': context['vendor']
        }

        # 身份识别能力的特殊处理
        if intent_type == IntentType.IDENTITY.value:
            return request_class(**request_params)
        else:
            return request_class(**request_params)

    async def route_to_capability_non_stream(self, context: Dict[str, Any]) -> str:
        """路由到相应的核心能力（非流式）"""
        intent_type = context["intent_result"].intent_type

        if intent_type not in self.capability_mapping:
            raise ValueError(INVALID_QUESTION_RESPONSE)

        # 创建请求对象
        capability_request = self._create_capability_request(intent_type, context)
        capability = self.capability_mapping[intent_type]['capability']

        # 调用相应的方法
        return await capability.generate_config(capability_request)

    async def route_to_capability_stream(self, context: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """路由到相应的核心能力（流式）"""
        intent_type = context["intent_result"].intent_type

        if intent_type not in self.capability_mapping:
            async for chunk in self._create_invalid_intent_stream():
                yield chunk
            return

        # 创建请求对象
        capability_request = self._create_capability_request(intent_type, context)
        capability = self.capability_mapping[intent_type]['capability']

        # 身份识别能力流式调用的特殊处理
        if intent_type == IntentType.IDENTITY.value:
            capability_request.stream = True

        async for chunk in capability.generate_config_stream(capability_request):
            yield chunk

    async def _create_invalid_intent_stream(self) -> AsyncGenerator[Dict[str, Any], None]:
        """创建无效意图的流式响应"""
        error_chunk = {
            "id": "chatcmpl-invalid",
            "object": "chat.completion.chunk",
            "created": 1677652288,
            "model": "config-generation",
            "choices": [
                {
                    "index": 0,
                    "delta": {"content": INVALID_QUESTION_RESPONSE},
                    "finish_reason": "stop"
                }
            ]
        }
        yield error_chunk


class ConfigGenerationService:
    """配置生成服务类"""

    def __init__(self):
        self.router = CapabilityRouter()

    async def _prepare_generation_context(self, request: ConfigGenerationRequest) -> Dict[str, Any]:
        """准备配置生成的上下文信息"""
        # 意图识别 - 使用新的消息格式方法
        intent_result = await intent_service.recognize_intent_from_messages(request)

        if not intent_result.success:
            return {
                "success": False,
                "error_message": intent_result.message,
                "intent_result": intent_result
            }

        # 获取设备类型和厂商信息
        device_type = request.device_type or intent_result.device_type or "unknown"
        vendor = request.vendor or intent_result.vendor or "unknown"

        logger.info(
            f"意图识别结果: {intent_result.intent_type}, "
            f"设备类型: {device_type}, 厂商: {vendor}"
        )

        return {
            "success": True,
            "intent_result": intent_result,
            "device_type": device_type,
            "vendor": vendor,
            "messages": request.messages,  # 传递完整的消息列表
            "user_input": intent_result.user_input or ""  # 从意图识别结果中获取用户输入
        }



    async def _create_error_stream(self, error_message: str) -> AsyncGenerator[Dict[str, Any], None]:
        """创建错误的流式响应"""
        error_chunk = {
            "id": "chatcmpl-error",
            "object": "chat.completion.chunk",
            "created": 1677652288,
            "model": "config-generation",
            "choices": [
                {
                    "index": 0,
                    "delta": {"content": error_message},
                    "finish_reason": "stop"
                }
            ]
        }
        yield error_chunk

    async def generate_config_stream(
        self,
        request: ConfigGenerationRequest
    ) -> AsyncGenerator[str, None]:
        """生成配置（流式返回）"""
        try:
            # 立即返回开始处理的消息
            start_chunk = {
                "id": "chatcmpl-start",
                "object": "chat.completion.chunk",
                "created": 1677652288,
                "model": "config-generation",
                "choices": [
                    {
                        "index": 0,
                        "delta": {"content": "正在分析您的请求..."},
                        "finish_reason": None
                    }
                ]
            }
            yield json.dumps(start_chunk, ensure_ascii=False)

            # 准备生成上下文
            context = await self._prepare_generation_context(request)

            if not context["success"]:
                # 意图识别失败，返回错误信息
                error_chunk = {
                    "id": "chatcmpl-intent-error",
                    "object": "chat.completion.chunk",
                    "created": 1677652288,
                    "model": "config-generation",
                    "choices": [
                        {
                            "index": 0,
                            "delta": {"content": f"\n\n{context['error_message']}"},
                            "finish_reason": "stop"
                        }
                    ]
                }
                yield json.dumps(error_chunk, ensure_ascii=False)
                return

            # 返回意图识别完成的消息
            intent_chunk = {
                "id": "chatcmpl-intent",
                "object": "chat.completion.chunk",
                "created": 1677652288,
                "model": "config-generation",
                "choices": [
                    {
                        "index": 0,
                        "delta": {"content": f"\n\n已识别意图类型：{context['intent_result'].intent_type}，正在生成配置..."},
                        "finish_reason": None
                    }
                ]
            }
            yield json.dumps(intent_chunk, ensure_ascii=False)

            # 路由到相应的核心能力
            capability_stream = self.router.route_to_capability_stream(context)

            async for chunk in capability_stream:
                yield json.dumps(chunk, ensure_ascii=False)

        except Exception as e:
            logger.error(f"配置生成流式处理失败: {str(e)}")
            error_chunk = {
                "id": "chatcmpl-error",
                "object": "chat.completion.chunk",
                "created": 1677652288,
                "model": "config-generation",
                "choices": [
                    {
                        "index": 0,
                        "delta": {"content": f"\n\n配置生成失败: {str(e)}"},
                        "finish_reason": "stop"
                    }
                ]
            }
            yield json.dumps(error_chunk, ensure_ascii=False)
    
    async def generate_config(
        self,
        request: ConfigGenerationRequest
    ) -> ConfigGenerationResponse:
        """生成配置（非流式返回）"""
        try:
            # 准备生成上下文
            context = await self._prepare_generation_context(request)

            if not context["success"]:
                return ConfigGenerationResponse(
                    success=False,
                    message=context["error_message"],
                    code=400,
                    intent_type=context["intent_result"].intent_type
                )

            # 路由到相应的核心能力
            config_content = await self.router.route_to_capability_non_stream(context)

            return ConfigGenerationResponse(
                success=True,
                message="配置生成成功",
                code=200,
                config_content=config_content,
                intent_type=context["intent_result"].intent_type,
                device_type=context["device_type"],
                vendor=context["vendor"]
            )

        except Exception as e:
            logger.error(f"配置生成失败: {str(e)}")
            return ConfigGenerationResponse(
                success=False,
                message=f"配置生成失败: {str(e)}",
                code=500
            )
    



# 全局配置生成服务实例
config_generation_service = ConfigGenerationService()
