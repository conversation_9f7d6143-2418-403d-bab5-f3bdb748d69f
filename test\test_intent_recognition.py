"""
意图识别服务单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock, patch

from app.services.intent_recognition import IntentRecognitionService
from app.models.request import IntentRecognitionRequest
from app.config.constants import IntentType


class TestIntentRecognitionService:
    """意图识别服务测试类"""

    def setup_method(self):
        """测试前准备"""
        self.service = IntentRecognitionService()

    def test_fallback_analysis_b_mvsp_nms(self):
        """测试B_MVSP_NMS意图的备用分析"""
        user_input = """
        因集团IPTV业务需求，需新联通云中原O域资源池通过承载B MVSP_NMS VPN与各省对接，
        申请资源池承载B网MVSP_NMS接收地址需求如下： 
        原接收地址范围由************/28调整为***********/27（河南）；
        新增接收地址范围***********/24（黑龙江）；
        """
        
        result = self.service._fallback_analysis(user_input)
        
        # 验证识别结果
        assert result['intent_type'] == IntentType.B_MVSP_NMS.value
        assert result['confidence'] >= 0.8

    def test_fallback_analysis_customized(self):
        """测试定制化意图的备用分析"""
        user_input = """
        CE端BGP AS号: 65001
        CE端设备: Router1
        设备型号: NE40E
        CE端口: GE0/0/1
        CE端互联IP地址: ***********/30
        """
        
        result = self.service._fallback_analysis(user_input)
        
        # 验证识别结果
        assert result['intent_type'] == IntentType.CUSTOMIZED.value
        assert result['confidence'] >= 0.7

    def test_fallback_analysis_general(self):
        """测试通用意图的备用分析"""
        user_input = "如何配置BGP协议？"
        
        result = self.service._fallback_analysis(user_input)
        
        # 验证识别结果
        assert result['intent_type'] == IntentType.GENERAL.value
        assert result['confidence'] >= 0.5

    def test_fallback_analysis_invalid(self):
        """测试无效意图的备用分析"""
        user_input = "今天天气怎么样？"
        
        result = self.service._fallback_analysis(user_input)
        
        # 验证识别结果
        assert result['intent_type'] == IntentType.INVALID.value
        assert result['confidence'] >= 0.8

    @pytest.mark.asyncio
    async def test_analyze_intent_with_llm_success(self):
        """测试LLM意图分析成功场景"""
        user_input = "承载B网MVSP_NMS接收地址调整"
        
        # Mock LLM响应
        mock_response = {
            "choices": [{
                "message": {
                    "content": """{
                        "intent_type": "b_mvsp_nms",
                        "confidence": 0.9,
                        "device_type": "router",
                        "vendor": "huawei",
                        "reasoning": "用户询问承载B网MVSP_NMS相关配置",
                        "extracted_info": {
                            "has_table": false,
                            "has_examples": false,
                            "config_keywords": ["承载B网", "MVSP_NMS", "接收地址"],
                            "ip_operations": ["modify"],
                            "ip_ranges": []
                        }
                    }"""
                }
            }]
        }
        
        with patch.object(self.service, '_call_llm_for_intent', return_value=mock_response):
            result = await self.service._analyze_intent_with_llm(user_input)
            
            # 验证分析结果
            assert result['intent_type'] == 'b_mvsp_nms'
            assert result['confidence'] == 0.9
            assert result['device_type'] == 'router'
            assert result['vendor'] == 'huawei'

    @pytest.mark.asyncio
    async def test_analyze_intent_with_llm_failure(self):
        """测试LLM意图分析失败场景"""
        user_input = "承载B网MVSP_NMS接收地址调整"
        
        # Mock LLM异常
        with patch.object(self.service, '_call_llm_for_intent', side_effect=Exception("LLM调用失败")):
            result = await self.service._analyze_intent_with_llm(user_input)
            
            # 应该返回备用分析结果
            assert result['intent_type'] == IntentType.B_MVSP_NMS.value

    @pytest.mark.asyncio
    async def test_recognize_intent_from_messages_b_mvsp_nms(self):
        """测试从消息中识别B_MVSP_NMS意图"""
        messages = [
            {"role": "user", "content": "承载B网MVSP_NMS接收地址范围调整需求"}
        ]
        
        # Mock LLM响应
        mock_response = {
            "choices": [{
                "message": {
                    "content": """{
                        "intent_type": "b_mvsp_nms",
                        "confidence": 0.95,
                        "device_type": "router",
                        "vendor": "huawei",
                        "reasoning": "用户询问承载B网MVSP_NMS相关配置",
                        "extracted_info": {
                            "has_table": false,
                            "has_examples": false,
                            "config_keywords": ["承载B网", "MVSP_NMS"],
                            "ip_operations": ["modify"],
                            "ip_ranges": []
                        }
                    }"""
                }
            }]
        }
        
        with patch.object(self.service, '_call_llm_for_intent', return_value=mock_response):
            response = await self.service.recognize_intent_from_messages(messages)
            
            # 验证响应
            assert response.success is True
            assert response.intent_type == 'b_mvsp_nms'
            assert response.confidence == 0.95
            assert response.device_type == 'router'
            assert response.vendor == 'huawei'

    @pytest.mark.asyncio
    async def test_recognize_intent_invalid(self):
        """测试识别无效意图"""
        request = IntentRecognitionRequest(user_input="今天天气怎么样？")
        
        # Mock LLM响应为无效意图
        mock_response = {
            "choices": [{
                "message": {
                    "content": """{
                        "intent_type": "invalid",
                        "confidence": 0.9,
                        "device_type": "unknown",
                        "vendor": "unknown",
                        "reasoning": "与网络设备配置无关",
                        "extracted_info": {
                            "has_table": false,
                            "has_examples": false,
                            "config_keywords": []
                        }
                    }"""
                }
            }]
        }
        
        with patch.object(self.service, '_call_llm_for_intent', return_value=mock_response):
            response = await self.service.recognize_intent(request)
            
            # 验证响应
            assert response.success is False
            assert response.intent_type == 'invalid'
            assert response.code == 400

    def test_b_mvsp_nms_keywords_detection(self):
        """测试B_MVSP_NMS关键词检测"""
        test_cases = [
            ("承载B网MVSP_NMS接收地址", True),
            ("MVSP_NMS地址范围调整", True),
            ("ip-prefix pl-MVSP_NMS配置", True),
            ("新增接收地址范围", True),
            ("回收原有地址列表", True),
            ("普通BGP配置", False),
            ("VLAN配置", False)
        ]
        
        for user_input, should_match in test_cases:
            result = self.service._fallback_analysis(user_input)
            
            if should_match:
                assert result['intent_type'] == IntentType.B_MVSP_NMS.value, f"应该识别为B_MVSP_NMS: {user_input}"
            else:
                assert result['intent_type'] != IntentType.B_MVSP_NMS.value, f"不应该识别为B_MVSP_NMS: {user_input}"

    @pytest.mark.asyncio
    async def test_extract_user_input_from_messages(self):
        """测试从消息中提取用户输入"""
        messages = [
            {"role": "system", "content": "系统消息"},
            {"role": "user", "content": "用户消息1"},
            {"role": "assistant", "content": "助手回复"},
            {"role": "user", "content": "最新用户消息"}
        ]
        
        user_input = self.service._extract_user_input_from_messages(messages)
        
        # 应该返回最新的用户消息
        assert user_input == "最新用户消息"

    @pytest.mark.asyncio
    async def test_extract_user_input_empty_messages(self):
        """测试空消息列表的处理"""
        messages = []
        
        user_input = self.service._extract_user_input_from_messages(messages)
        
        # 应该返回空字符串
        assert user_input == ""

    @pytest.mark.asyncio
    async def test_extract_user_input_no_user_messages(self):
        """测试没有用户消息的情况"""
        messages = [
            {"role": "system", "content": "系统消息"},
            {"role": "assistant", "content": "助手回复"}
        ]
        
        user_input = self.service._extract_user_input_from_messages(messages)
        
        # 应该返回空字符串
        assert user_input == ""


if __name__ == "__main__":
    pytest.main([__file__])
