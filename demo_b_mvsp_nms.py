#!/usr/bin/env python3
"""
承载B网MVSP_NMS配置生成能力演示脚本
"""

import asyncio
from app.core.b_mvsp_nms_capability import BMVSPNMSCapability
from app.services.intent_recognition import IntentRecognitionService
from app.models.request import ConfigGenerationRequest, Message


async def demo_intent_recognition():
    """演示意图识别功能"""
    print("=" * 60)
    print("承载B网MVSP_NMS意图识别演示")
    print("=" * 60)
    
    service = IntentRecognitionService()
    
    test_inputs = [
        "因集团IPTV业务需求，需新联通云中原O域资源池通过承载B MVSP_NMS VPN与各省对接",
        "申请资源池承载B网MVSP_NMS接收地址需求",
        "原接收地址范围由************/28调整为***********/27",
        "新增接收地址范围***********/24",
        "回收原有地址列表",
        "普通BGP配置问题"  # 这个应该不被识别为B_MVSP_NMS
    ]
    
    for user_input in test_inputs:
        print(f"\n输入: {user_input}")
        result = service._fallback_analysis(user_input)
        print(f"识别结果: {result['intent_type']}")
        print(f"置信度: {result['confidence']}")
        print(f"推理: {result['reasoning']}")


async def demo_llm_analysis():
    """演示LLM分析功能"""
    print("\n" + "=" * 60)
    print("LLM分析演示")
    print("=" * 60)

    capability = BMVSPNMSCapability()

    test_input = """
    因集团IPTV业务需求，需新联通云中原O域资源池通过承载B MVSP_NMS VPN与各省对接，申请资源池承载B网MVSP_NMS接收地址需求如下：
    原接收地址范围由************/28调整为***********/27（河南）；
    新增接收地址范围***********/24（黑龙江）；
    """

    print(f"输入文本:\n{test_input}")
    print("\n使用LLM分析用户需求并生成配置...")

    try:
        # 模拟LLM分析（实际会调用LLM服务）
        config = await capability._generate_config_with_llm(test_input, "router", "huawei")
        print(f"\nLLM生成的配置:\n{config}")
    except Exception as e:
        print(f"LLM分析失败: {e}")
        print("注意: 需要配置LLM服务才能正常工作")


async def demo_config_generation():
    """演示配置生成功能"""
    print("\n" + "=" * 60)
    print("配置生成演示")
    print("=" * 60)
    
    capability = BMVSPNMSCapability()
    
    # 华为设备配置生成
    print("华为设备配置生成:")
    print("-" * 30)
    
    messages = [
        Message(
            role="user",
            content="""因集团IPTV业务需求，需新联通云中原O域资源池通过承载B MVSP_NMS VPN与各省对接，申请资源池承载B网MVSP_NMS接收地址需求如下： 
            原接收地址范围由************/28调整为***********/27（河南）；
            新增接收地址范围***********/24（黑龙江）；
            
            #原有的配置
            ip ip-prefix pl-MVSP_NMS_B-v4-in index 10 permit ************ 28"""
        )
    ]
    
    request = ConfigGenerationRequest(
        messages=messages,
        device_type="router",
        vendor="huawei"
    )
    
    config = await capability.generate_config(request)
    print(config)
    
    # 思科设备配置生成
    print("\n思科设备配置生成:")
    print("-" * 30)
    
    cisco_messages = [
        Message(
            role="user",
            content="新增承载B网MVSP_NMS接收地址范围***********/27和***********/24"
        )
    ]
    
    cisco_request = ConfigGenerationRequest(
        messages=cisco_messages,
        device_type="router",
        vendor="cisco"
    )
    
    cisco_config = await capability.generate_config(cisco_request)
    print(cisco_config)


async def demo_stream_generation():
    """演示流式配置生成"""
    print("\n" + "=" * 60)
    print("流式配置生成演示")
    print("=" * 60)
    
    capability = BMVSPNMSCapability()
    
    messages = [
        Message(
            role="user",
            content="新增承载B网MVSP_NMS接收地址范围***********/24"
        )
    ]
    
    request = ConfigGenerationRequest(
        messages=messages,
        device_type="router",
        vendor="huawei"
    )
    
    print("流式响应:")
    chunk_count = 0
    async for chunk in capability.generate_config_stream(request):
        chunk_count += 1
        print(f"Chunk {chunk_count}: {chunk['object']}")
        if 'content' in chunk['choices'][0]['delta']:
            content = chunk['choices'][0]['delta']['content']
            if content.strip():
                print(f"内容: {repr(content[:50])}...")


async def main():
    """主演示函数"""
    print("承载B网MVSP_NMS配置生成能力演示")
    print("=" * 60)
    
    try:
        await demo_intent_recognition()
        await demo_llm_analysis()
        await demo_config_generation()
        await demo_stream_generation()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
